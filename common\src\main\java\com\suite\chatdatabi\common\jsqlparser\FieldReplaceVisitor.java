package com.suite.chatdatabi.common.jsqlparser;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.OrderByElement;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class FieldReplaceVisitor extends ExpressionVisitorAdapter {

    private Map<String, String> fieldNameMap;
    private ThreadLocal<Boolean> exactReplace = ThreadLocal.withInitial(() -> false);

    public FieldReplaceVisitor(Map<String, String> fieldNameMap, boolean exactReplace) {
        this.fieldNameMap = fieldNameMap;
        this.exactReplace.set(exactReplace);
    }

    @Override
    public void visit(Column column) {
        SqlReplaceHelper.replaceColumn(column, fieldNameMap, exactReplace.get());
    }

    @Override
    public void visit(Function function) {
        boolean originalExactReplace = exactReplace.get();
        exactReplace.set(true);
        try {
            super.visit(function);
        } finally {
            exactReplace.set(originalExactReplace);
        }
    }

    @Override
    public void visit(AnalyticExpression expr) {
        super.visit(expr);
        WindowDefinition windowDefinition = expr.getWindowDefinition();

        // 处理窗口定义为空的情况
        if (windowDefinition == null) {
            return;
        }

        ExpressionList partitionExpressionList = windowDefinition.getPartitionExpressionList();

        // 处理 PARTITION BY 子句
        if (partitionExpressionList != null && partitionExpressionList.getExpressions() != null) {
            for (Object obj : partitionExpressionList.getExpressions()) {
                // 优先进行运行时类型检查与安全转换
                if (obj instanceof Expression partitionExpr) {
                    partitionExpr.accept(this);
                } else {
                    throw new IllegalArgumentException(
                            "Unexpected element type in partition expressions");
                }
            }
        }
        // 处理 ORDER BY 元素
        if (!CollectionUtils.isEmpty(windowDefinition.getOrderByElements())) {
            for (OrderByElement element : windowDefinition.getOrderByElements()) {
                Expression expression = element.getExpression();
                // 避免直接访问可能为空的对象属性
                if (expression != null) {
                    expression.accept(this);
                }
            }
        }
    }

}
