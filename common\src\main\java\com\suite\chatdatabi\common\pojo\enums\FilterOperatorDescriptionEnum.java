package com.suite.chatdatabi.common.pojo.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 过滤操作符描述枚举：将 FilterOperatorEnum 映射到中文描述
 */
public enum FilterOperatorDescriptionEnum {
    IN(FilterOperatorEnum.IN, "属于"),
    NOT_IN(FilterOperatorEnum.NOT_IN, "不属于"),
    EQUALS(FilterOperatorEnum.EQUALS, "等于"),
    BETWEEN(FilterOperatorEnum.BETWEEN, "介于"),
    GREATER_THAN(FilterOperatorEnum.GREATER_THAN, "大于"),
    GREATER_THAN_EQUALS(FilterOperatorEnum.GREATER_THAN_EQUALS, "大于等于"),
    IS_NULL(FilterOperatorEnum.IS_NULL, "为空"),
    IS_NOT_NULL(FilterOperatorEnum.IS_NOT_NULL, "不为空"),
    LIKE(FilterOperatorEnum.LIKE, "包含"),
    MINOR_THAN(FilterOperatorEnum.MINOR_THAN, "小于"),
    MINOR_THAN_EQUALS(FilterOperatorEnum.MINOR_THAN_EQUALS, "小于等于"),
    NOT_EQUALS(FilterOperatorEnum.NOT_EQUALS, "不等于"),
    SQL_PART(FilterOperatorEnum.SQL_PART, "SQL片段"),
    EXISTS(FilterOperatorEnum.EXISTS, "存在");

    private final FilterOperatorEnum operator;
    /**
     * -- GETTER -- 获取操作符对应的描述
     */
    @Getter
    private final String description;

    // 静态映射表：用于快速查找
    private static final Map<FilterOperatorEnum, String> DESCRIPTION_MAP = new HashMap<>();

    static {
        // 初始化映射表
        for (FilterOperatorDescriptionEnum desc : values()) {
            DESCRIPTION_MAP.put(desc.operator, desc.description);
        }
    }

    FilterOperatorDescriptionEnum(FilterOperatorEnum operator, String description) {
        this.operator = operator;
        this.description = description;
    }

    /**
     * 根据 FilterOperatorEnum 获取对应的描述
     */
    public static String getDescription(FilterOperatorEnum operator) {
        return DESCRIPTION_MAP.getOrDefault(operator, operator.name());
    }

    /**
     * 检查是否为值比较操作符
     */
    public static boolean isValueCompare(FilterOperatorEnum operator) {
        return operator == FilterOperatorEnum.EQUALS || operator == FilterOperatorEnum.GREATER_THAN
                || operator == FilterOperatorEnum.GREATER_THAN_EQUALS
                || operator == FilterOperatorEnum.MINOR_THAN
                || operator == FilterOperatorEnum.MINOR_THAN_EQUALS
                || operator == FilterOperatorEnum.NOT_EQUALS;
    }


}
