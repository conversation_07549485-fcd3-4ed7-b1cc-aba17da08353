package com.suite.chatdatabi.chat.server.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.printer.Printer;
import com.suite.chatdatabi.agent.printer.SSEPrinter;
import com.suite.chatdatabi.agent.tool.ToolCollection;
import com.suite.chatdatabi.agent.tool.bank.BankDataReportTool;
import com.suite.chatdatabi.agent.tool.bank.BankDataPlanningTool;
import com.suite.chatdatabi.agent.tool.bank.BankingQuestionClassifierTool;
import com.suite.chatdatabi.agent.tool.bank.EChartsRenderTool;
import com.suite.chatdatabi.agent.tool.common.CodeInterpreterTool;
import com.suite.chatdatabi.agent.tool.common.DeepSearchTool;
import com.suite.chatdatabi.agent.tool.common.FileTool;
import com.suite.chatdatabi.agent.tool.common.ReportTool;
import com.suite.chatdatabi.agent.tool.mcp.McpTool;
import com.suite.chatdatabi.agent.util.DateUtil;
import com.suite.chatdatabi.agent.util.ThreadUtil;
import com.suite.chatdatabi.config.GenieConfig;
import com.suite.chatdatabi.model.req.AgentRequest;
import com.suite.chatdatabi.model.req.GptQueryReq;
import com.suite.chatdatabi.service.AgentHandlerService;
import com.suite.chatdatabi.service.IGptProcessService;
import com.suite.chatdatabi.service.impl.AgentHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/")
public class MultiAgentController {
    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(5);
    private static final long HEARTBEAT_INTERVAL = 10_000L; // 10秒心跳间隔
    @Autowired
    protected GenieConfig genieConfig;
    @Autowired
    private AgentHandlerFactory agentHandlerFactory;
    @Autowired
    private IGptProcessService gptProcessService;



    /**
     * 开启SSE心跳
     * @param emitter
     * @param requestId
     * @return
     */
    private ScheduledFuture<?> startHeartbeat(SseEmitter emitter, String requestId) {
        return executor.scheduleAtFixedRate(() -> {
            try {
                // 发送心跳消息
//                log.info("{} send heartbeat", requestId);
                emitter.send("heartbeat");
            } catch (Exception e) {
                // 发送心跳失败，关闭连接
//                log.error("{} heartbeat failed, closing connection", requestId, e);
                emitter.completeWithError(e);
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.MILLISECONDS);
    }

    /**
     * 注册SSE事件
     * @param emitter
     * @param requestId
     * @param heartbeatFuture
     */
    private void registerSSEMonitor(SseEmitter emitter, String requestId, ScheduledFuture<?> heartbeatFuture) {
        // 监听SSE异常事件
        emitter.onCompletion(() -> {
//            log.info("{} SSE connection completed normally", requestId);
            heartbeatFuture.cancel(true);
        });

        // 监听连接超时事件
        emitter.onTimeout(() -> {
//            log.info("{} SSE connection timed out", requestId);
            heartbeatFuture.cancel(true);
            emitter.complete();
        });

        // 监听连接错误事件
        emitter.onError((ex) -> {
//            log.info("{} SSE connection error: ", requestId, ex);
            heartbeatFuture.cancel(true);
            emitter.completeWithError(ex);
        });
    }

    /**
     * 执行智能体调度
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/AutoAgent")
    public SseEmitter AutoAgent(@RequestBody AgentRequest request) throws UnsupportedEncodingException {

//        log.info("{} auto agent request: {}", request.getRequestId(), JSON.toJSONString(request));

        Long AUTO_AGENT_SSE_TIMEOUT = 60 * 60 * 1000L;

        SseEmitter emitter = new SseEmitter(AUTO_AGENT_SSE_TIMEOUT);
        // SSE心跳
        ScheduledFuture<?> heartbeatFuture = startHeartbeat(emitter, request.getRequestId());
        // 监听SSE事件
        registerSSEMonitor(emitter, request.getRequestId(), heartbeatFuture);
        // 拼接输出类型
        request.setQuery(handleOutputStyle(request));
        // 执行调度引擎
        ThreadUtil.execute(() -> {
            try {
                Printer printer = new SSEPrinter(emitter, request, request.getAgentType());
                AgentContext agentContext = AgentContext.builder()
                        .requestId(request.getRequestId())
                        .sessionId(request.getRequestId())
                        .printer(printer)
                        .query(request.getQuery())
                        .task("")
                        .dateInfo(DateUtil.CurrentDateInfo())
                        .productFiles(new ArrayList<>())
                        .taskProductFiles(new ArrayList<>())
                        .sopPrompt(request.getSopPrompt())
                        .basePrompt(request.getBasePrompt())
                        .agentType(request.getAgentType())
                        .scenario("") //增加默认提示词为银行分析
                        .toolResult(new HashMap<>())
                        .isStream(Objects.nonNull(request.getIsStream()) ? request.getIsStream() : false)
                        .build();
                // 新任务开始前清理工具结果
                agentContext.clearToolResults();
                log.info("=============智能体调度开始=============");
                // 构建工具列表
                agentContext.setToolCollection(buildToolCollection(agentContext, request));
                log.info("=============agentContext内容=============" );
                // 根据数据类型获取对应的处理器
                AgentHandlerService handler = agentHandlerFactory.getHandler(agentContext, request);
                log.info("==============={} 获取处理器: {}", request.getRequestId(), handler.getClass().getName());
                // 执行处理逻辑
                handler.handle(agentContext, request);
                log.info("=============智能体调度结束=============");
                // 关闭连接
                emitter.complete();

            } catch (Exception e) {
                log.error("{} auto agent error", request.getRequestId(), e);
            }
        });

        return emitter;
    }


    /**
     * html模式： query+以 html展示
     * docs模式：query+以 markdown展示
     * table 模式: query+以 excel 展示
     */
    private String handleOutputStyle(AgentRequest request) {
        String query = request.getQuery();
//        log.info("========={} 输出模式 query: {}", request.getRequestId(), query);
        Map<String, String> outputStyleMap = genieConfig.getOutputStylePrompts();
        if (!StringUtils.isEmpty(request.getOutputStyle())) {
            query += outputStyleMap.computeIfAbsent(request.getOutputStyle(), k -> "");
        }
        return query;
    }


    /**
     * 构建工具列表
     *
     * @param agentContext
     * @param request
     * @return
     */
    private ToolCollection buildToolCollection(AgentContext agentContext, AgentRequest request) {

        ToolCollection toolCollection = new ToolCollection();
        toolCollection.setAgentContext(agentContext);
        //判断用户问题分类
        BankingQuestionClassifierTool bankTool = new BankingQuestionClassifierTool();
        bankTool.setAgentContext(agentContext);
        toolCollection.addTool(bankTool);

        // 添加银行数据分析问题分类工具
        BankDataPlanningTool bankDataPlanningTool = new BankDataPlanningTool();
        bankDataPlanningTool.setAgentContext(agentContext);
        toolCollection.addTool(bankDataPlanningTool);

        // file
        FileTool fileTool = new FileTool();
        fileTool.setAgentContext(agentContext);
        toolCollection.addTool(fileTool);

        // default tool
        List<String> agentToolList = Arrays.asList(genieConfig.getMultiAgentToolListMap()
                .getOrDefault("default", "search,code,report").split(","));
//        log.info("========={} 构建工具列表: {}", request.getRequestId(), agentToolList);
        if (!agentToolList.isEmpty()) {
            if (agentToolList.contains("code")) {
                CodeInterpreterTool codeTool = new CodeInterpreterTool();
                codeTool.setAgentContext(agentContext);
//                log.info("========={} 构建工具列表code: {}", request.getRequestId(), codeTool.getName());
                toolCollection.addTool(codeTool);
            }
            if (agentToolList.contains("report")) {
                // 检查是否为银行分析场景
                if (request.getQuery().contains("查询") || request.getQuery().contains("获取") || request.getQuery().contains("趋势") ||
                        request.getQuery().contains("分析") ||  request.getQuery().contains("原因") || request.getQuery().contains("根因") ||request.getQuery().contains("报告")) {
                    // 初始化ECharts工具
                    EChartsRenderTool eChartsRenderTool = new EChartsRenderTool();
                    eChartsRenderTool.setAgentContext(agentContext);
                    toolCollection.addTool(eChartsRenderTool);
                    //添加银行分析报告生成工具
                    BankDataReportTool bankReportTool = new BankDataReportTool();
                    bankReportTool.setAgentContext(agentContext);
                    toolCollection.addTool(bankReportTool);
                    log.info("========={} 构建y银行分析报告工具列表report: {}", request.getRequestId(), bankReportTool.getName());
                }else {
                    ReportTool htmlTool = new ReportTool();
                    htmlTool.setAgentContext(agentContext);
                    log.info("========={} 构建工具列表report: {}", request.getRequestId(), htmlTool.getName());
                    toolCollection.addTool(htmlTool);
                }
            }
            if (agentToolList.contains("search")) {
                DeepSearchTool deepSearchTool = new DeepSearchTool();
                deepSearchTool.setAgentContext(agentContext);
                toolCollection.addTool(deepSearchTool);
            }
        }

        // mcp tool
        try {
            McpTool mcpTool = new McpTool();
            mcpTool.setAgentContext(agentContext);
            for (String mcpServer : genieConfig.getMcpServerUrlArr()) {
                String listToolResult = mcpTool.listTool(mcpServer);
                if (listToolResult.isEmpty()) {
                    continue;
                }

                JSONObject resp = JSON.parseObject(listToolResult);
                if (resp.getIntValue("code") != 200) {
                    continue;
                }
                JSONArray data = resp.getJSONArray("data");
                if (data.isEmpty()) {
                    continue;
                }
                for (int i = 0; i < data.size(); i++) {
                    JSONObject tool = data.getJSONObject(i);
                    String method = tool.getString("name");
                    String description = tool.getString("description");
                    String inputSchema = tool.getString("inputSchema");
                    toolCollection.addMcpTool(method, description, inputSchema, mcpServer);
                    log.info("========={} 构建工具列表mcp: {}", request.getRequestId(), method);
                }
            }
        } catch (Exception e) {
//            log.error("{} add mcp tool failed", agentContext.getRequestId(), e);
        }

        return toolCollection;
    }

    /**
     * 探活接口
     *
     * @return
     */
    @RequestMapping(value = "/web/health", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("ok");
    }


    /**
     * 处理Agent流式增量查询请求，返回SSE事件流
     * @param params 查询请求参数对象，包含GPT查询所需信息
     * @return 返回SSE事件发射器，用于流式传输增量响应结果
     */
    @RequestMapping(value = "/web/api/v1/gpt/queryAgentStreamIncr", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter queryAgentStreamIncr(@RequestBody GptQueryReq params) {
        genieConfig.init();
        return gptProcessService.queryMultiAgentIncrStream(params);
    }

}
    