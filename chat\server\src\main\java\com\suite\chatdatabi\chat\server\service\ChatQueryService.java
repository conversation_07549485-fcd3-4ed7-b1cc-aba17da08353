package com.suite.chatdatabi.chat.server.service;

import com.suite.chatdatabi.chat.api.pojo.request.ChatExecuteReq;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.chat.api.pojo.request.ChatQueryDataReq;
import com.suite.chatdatabi.chat.api.pojo.response.ChatParseResp;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.util.ChatParseEvent;
import com.suite.chatdatabi.chat.server.util.ExecuteResultEvent;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.headless.api.pojo.request.DimensionValueReq;
import com.suite.chatdatabi.headless.api.pojo.response.SearchResult;
import jakarta.servlet.http.HttpServletResponse;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ChatQueryService {

    List<SearchResult> search(ChatParseReq chatParseReq);

    ChatParseResp parse(ChatParseReq chatParseReq);

    public String nl2Data(ChatParseReq chatParseReq);

    Flux<ChatParseEvent> difyParse(ChatParseReq chatParseReq);

    QueryResult execute(ChatExecuteReq chatExecuteReq) throws Exception;

    String getChatHistoryQuery(ChatExecuteReq chatExecuteReq) throws Exception;

    QueryResult parseAndExecute(ChatParseReq chatParseReq);

    Object queryData(ChatQueryDataReq chatQueryDataReq, User user) throws Exception;

    Object queryDimensionValue(DimensionValueReq dimensionValueReq, User user) throws Exception;

    Flux<ChatParseEvent> parseStream(ChatParseReq chatParseReq);

    Flux<ExecuteResultEvent> executeStream(ChatExecuteReq chatExecuteReq);

    Map<String, Object> saveDirectQA(Integer agentId, Long chatId, String queryText, User user,
            Integer parseId, QueryResult queryResult, String answer);

    /**
     * 数据分析解读
     *
     * @param chatExecuteReq 解析请求
     * @param response
     * @return 解读结果
     */
    Flux<ExecuteResultEvent> summary(ChatExecuteReq chatExecuteReq, HttpServletResponse response);

    /**
     * 数据解读非流式
     * @param chatExecuteReq
     * @param response
     * @return
     */
    ExecuteResultEvent summaryAsync(ChatExecuteReq chatExecuteReq, HttpServletResponse response);


}
