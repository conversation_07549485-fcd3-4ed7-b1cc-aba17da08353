package com.suite.chatdatabi.attribution;


import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import lombok.extern.slf4j.Slf4j;


import java.util.Map;
import java.util.Optional;
import java.util.Set;


@Slf4j
public class SegmentTextNode implements NodeAction {

    private GraphQueryService graphQueryService;

    public SegmentTextNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state) {
        Optional<Object> queryText = state.value("queryText");
        Optional<Object> agentId = state.value("agentId");

        if (queryText.isEmpty() || agentId.isEmpty()) {
            return Map.of();
        }

        Set<String> strings = graphQueryService.segmentText(queryText.get().toString(), Integer.valueOf(agentId.get().toString()));
        log.info("SegmentTextNode 获取到 queryText ------>{}",queryText);
        log.info("SegmentTextNode 获取到 segmentResult ------>{}",strings);


        return Map.of("segmentResult",strings);
    }
}
