package com.suite.chatdatabi.agent.agent;

import com.suite.chatdatabi.agent.dto.File;
import com.suite.chatdatabi.agent.printer.Printer;
import com.suite.chatdatabi.agent.tool.ToolCollection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class AgentContext {
    String requestId;
    String sessionId;
    String query;
    String task;
    Printer printer;
    ToolCollection toolCollection;
    String dateInfo;
    List<File> productFiles;
    Boolean isStream;
    String streamMessageType;
    String sopPrompt;
    String basePrompt;
    Integer agentType;
    List<File> taskProductFiles;
    String scenario; // 新增场景字段，判断是否是银行数据分析场景，如果银行分析场景走银行分析场景提示词
    Map<String, Object> toolResult; // 存储银行数据分析过程工具执行结果，存储得数据需要在生成报告时使用
    /**
     * 清理工具执行结果
     * 使用场景：
     * 1. 新任务开始前清理上一个任务的结果
     * 2. 会话重置时清理所有工具结果
     * 3. 任务完成后根据配置清理结果
     */
    public void clearToolResults() {
        if (toolResult != null) {
            toolResult.clear();
            log.info("{} 清理工具执行结果", requestId);
        } else {
            log.debug("{} 工具执行结果为空，无需清理", requestId);
        }
    }

    /**
     * 添加单个工具结果
     * 使用场景：
     * 1. 单独保存某个工具的执行结果
     * 2. 外部系统注入工具结果
     * 3. 测试场景中模拟工具结果
     *
     * @param toolName 工具名称
     * @param result 工具执行结果
     */
    public void addToolResult(String toolName, Object result) {
        if (toolResult == null) {
            toolResult = new HashMap<>();
        }
        toolResult.put(toolName, result);
        log.info("{} 添加工具结果: {} -> {}", requestId, toolName,
                result != null ? result.getClass().getSimpleName() : "null");
    }

    /**
     * 获取指定工具的执行结果
     *
     * @param toolName 工具名称
     * @return 工具执行结果，如果不存在返回null
     */
    public Object getToolResult(String toolName) {
        if (toolResult == null) {
            return null;
        }
        return toolResult.get(toolName);
    }

    /**
     * 检查是否存在指定工具的执行结果
     *
     * @param toolName 工具名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasToolResult(String toolName) {
        return toolResult != null && toolResult.containsKey(toolName);
    }

    /**
     * 获取所有工具执行结果
     *
     * @return 所有工具执行结果的Map，如果为空返回空Map
     */
    public Map<String, Object> getAllToolResults() {
        return toolResult != null ? toolResult : new HashMap<>();
    }
    @Override
    public String toString() {
        return "AgentContext{" +
                "requestId='" + requestId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", query='" + query + '\'' +
                ", task='" + task + '\'' +
                ", agentType=" + agentType +
                '}';
    }
}