package com.suite.chatdatabi.chat.server.service;

import com.suite.chatdatabi.chat.server.agent.Agent;
import com.suite.chatdatabi.common.pojo.ChatApp;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.provider.ModelProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class IntentRecognitionService {

    public enum IntentType {
        数据查询,归因分析,生成报告, DIFY
    }

    public IntentType recognizeIntent(String queryText, Agent agent) {
        // 确保queryText不为空
        if (StringUtils.isBlank(queryText)) {
            log.warn("Query text is blank, cannot recognize intent");
            return null;
        }
        if (!agent.enableIntentRecognition()) {
            return IntentType.数据查询; // 默认为数据查询
        }

        // 构建变量映射时确保包含question变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("question", queryText);
        try {
            ChatApp intentApp = agent.getChatAppConfig().get("INTENT_RECOGNITION");
            if (Objects.isNull(intentApp) || !intentApp.isEnable()) {
                return IntentType.数据查询;
            }

            String promptStr = String.format(intentApp.getPrompt(), queryText);
            Prompt prompt = PromptTemplate.from(promptStr).apply(variables);
            ChatLanguageModel chatLanguageModel = ModelProvider.getChatModel(intentApp.getChatModelConfig());
            Response<AiMessage> response = chatLanguageModel.generate(prompt.toUserMessage());

            String result = response.content().text().trim();
            log.info("Intent recognition result for '{}': {}", queryText, result);

            // 支持所有四种意图类型的识别
            try {
                return IntentType.valueOf(result);
            } catch (IllegalArgumentException e) {
                log.warn("Unrecognized intent type '{}', defaulting to 数据查询", result);
                return IntentType.数据查询;
            }

        } catch (Exception e) {
            log.error("Intent recognition failed for query: {}", queryText, e);
            return IntentType.数据查询; // 异常时默认为数据查询
        }
    }
}