package com.suite.chatdatabi.service.impl;

import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.agent.ReActAgent;
import com.suite.chatdatabi.agent.agent.ReactImplAgent;
import com.suite.chatdatabi.agent.agent.SummaryAgent;
import com.suite.chatdatabi.agent.dto.File;
import com.suite.chatdatabi.agent.dto.Message;
import com.suite.chatdatabi.agent.dto.TaskSummaryResult;
import com.suite.chatdatabi.agent.enums.AgentType;
import com.suite.chatdatabi.config.GenieConfig;
import com.suite.chatdatabi.model.req.AgentRequest;
import com.suite.chatdatabi.service.AgentHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ReactHandlerImpl implements AgentHandlerService {

    private static final Logger log = LoggerFactory.getLogger(ReactHandlerImpl.class);
    @Autowired
    private GenieConfig genieConfig;


    @Override
    public String handle(AgentContext agentContext, AgentRequest request) {
        // 检查是否为银行分析场景
        if (request.getQuery().contains("查询") || request.getQuery().contains("获取") || request.getQuery().contains("趋势") ||
                request.getQuery().contains("分析") ||  request.getQuery().contains("原因") || request.getQuery().contains("根因") ||request.getQuery().contains("报告")) {
            agentContext.setScenario("bank_analysis");
        }
        ReActAgent executor = new ReactImplAgent(agentContext);
        SummaryAgent summary = new SummaryAgent(agentContext);
        summary.setSystemPrompt(summary.getSystemPrompt().replace("{{query}}", request.getQuery()));
        executor.run(request.getQuery());
        TaskSummaryResult result = null;
        // 根据场景选择不同的总结方法
        if (isBankDataAnalysisQuery(request.getQuery())) {
            Map<String, Object> analysisData = agentContext.getToolResult();
             result= summary.summaryBankingTaskResult(
                    executor.getMemory().getMessages(), request.getQuery(), analysisData, true);
             log.info("===========summaryBankingTaskResult任务总结结果: {}", result);
        }else {
            summary.summaryTaskResult(
                    executor.getMemory().getMessages(), request.getQuery());
        }
        Map<String, Object> taskResult = new HashMap<>();
        taskResult.put("taskSummary", result.getTaskSummary());

        if (CollectionUtils.isEmpty(result.getFiles())) {
            if (!CollectionUtils.isEmpty(agentContext.getProductFiles())) {
                List<File> fileResponses = agentContext.getProductFiles();
                // 过滤中间搜索结果文件
                fileResponses.removeIf(file -> Objects.nonNull(file) && file.getIsInternalFile());
                Collections.reverse(fileResponses);
                taskResult.put("fileList", fileResponses);
            }
        } else {
            taskResult.put("fileList", result.getFiles());
        }

        log.info("===========taskSummaryResult任务总结结果: {}", result);
        agentContext.getPrinter().send("result", taskResult);

        return "";
    }

    private boolean isBankDataAnalysisQuery(String query) {
        if (query == null) return false;
        String[] bankKeywords = { "查询", "统计", "银行", "分析","原因",  "生成报告", "输出报告", "报告", "总结", "汇总", "获取", "根因"};
        String lowerQuery = query.toLowerCase();
        return Arrays.stream(bankKeywords).anyMatch(keyword ->
                lowerQuery.contains(keyword.toLowerCase()));
    }


    @Override
    public Boolean support(AgentContext agentContext, AgentRequest request) {
        return AgentType.REACT.getValue().equals(request.getAgentType());
    }
}
