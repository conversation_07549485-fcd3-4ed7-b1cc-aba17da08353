package com.suite.chatdatabi.chat.server.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.chat.api.pojo.request.PageQueryInfoReq;
import com.suite.chatdatabi.chat.api.pojo.response.ChatParseResp;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResp;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatParseDO;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.headless.api.pojo.SemanticParseInfo;

import java.util.List;

public interface ChatQueryRepository {

    PageInfo<QueryResp> getChatQuery(PageQueryInfoReq pageQueryInfoCommend, Long chatId);

    QueryResp getChatQuery(Long queryId);

    List<QueryResp> getChatQueries(Integer chatId);

    ChatQueryDO getChatQueryDO(Long queryId);

    List<QueryResp> queryShowCase(PageQueryInfoReq pageQueryInfoCommend, int agentId);

    int updateChatQuery(ChatQueryDO chatQueryDO);

    void updateChatQuery(ChatQueryDO chatQueryDO, UpdateWrapper<ChatQueryDO> updateWrapper);

    Long createChatQuery(ChatParseReq chatParseReq);

    Long saveChatQuery(ChatQueryDO chatQueryDO);

    List<ChatParseDO> batchSaveParseInfo(ChatParseReq chatParseReq, ChatParseResp chatParseResp,
                                         List<SemanticParseInfo> candidateParses);

    ChatParseDO getParseInfo(Long questionId, int parseId);

    List<ChatParseDO> getParseInfoList(List<Long> questionIds);
}
