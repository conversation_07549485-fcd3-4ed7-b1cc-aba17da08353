package com.suite.chatdatabi.agent.agent;

import com.suite.chatdatabi.agent.dto.File;
import com.suite.chatdatabi.agent.dto.Message;
import com.suite.chatdatabi.agent.dto.TaskSummaryResult;
import com.suite.chatdatabi.agent.llm.LLM;
import com.suite.chatdatabi.agent.tool.bank.BankDataReportTool;
import com.suite.chatdatabi.agent.util.SpringContextHolder;
import com.suite.chatdatabi.config.GenieConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class SummaryAgent extends BaseAgent {
    private String requestId;
    private Integer messageSizeLimit;
    public static final String logFlag = "summaryTaskResult";

    public SummaryAgent(AgentContext context) {
        ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();
        GenieConfig genieConfig = applicationContext.getBean(GenieConfig.class);
        // 检查是否为银行数据分析任务，切换相应的系统提示词
        String systemPrompt = genieConfig.getSummarySystemPrompt();
        if (isBankingAnalysisTask(context.getQuery())) {
            log.info("============替换为银行数据分析提示词{}", requestId);
            systemPrompt = genieConfig.getSummaryBankAnalysisPrompt();
        }
        setSystemPrompt(systemPrompt);

        setContext(context);
        setRequestId(context.getRequestId());
        setLlm(new LLM(context.getAgentType() == 3 ? genieConfig.getPlannerModelName() : genieConfig.getReactModelName(), ""));
        setMessageSizeLimit(genieConfig.getMessageSizeLimit());
    }

    /**
     * 执行单个步骤
     */
    public String step() {
        return "";
    }

    // 构造文件信息
    private String createFileInfo() {
        List<File> files = context.getProductFiles();
        if (CollectionUtils.isEmpty(files)) {
            log.info("requestId: {} no files found in context", requestId);
            return "";
        }
        log.info("requestId: {} {} product files:{}", requestId, logFlag, files);
        String result = files.stream()
                .filter(file -> !file.getIsInternalFile()) // 过滤内部文件
                .map(file -> file.getFileName() + " : " + file.getDescription())
                .collect(Collectors.joining("\n"));

        log.info("requestId: {} generated file info: {}", requestId, result);
        return result;
    }

    // 提取系统提示格式化逻辑
    private String formatSystemPrompt(String taskHistory, String query) {
        String systemPrompt = getSystemPrompt();
        if (systemPrompt == null) {
            log.error("requestId: {} {} systemPrompt is null", requestId, logFlag);
            throw new IllegalStateException("System prompt is not configured");
        }
        // 替换占位符
        return systemPrompt
                .replace("{{taskHistory}}", taskHistory)
                .replace("{{fileNameDesc}}", createFileInfo())
                .replace("{{query}}", query);
    }

    // 提取消息创建逻辑
    private Message createSystemMessage(String content) {
        return Message.userMessage(content, null); // 如果需要更复杂的消息构建，可扩展
    }

    /**
     * 解析LLM响应并处理文件关联
     */
    private TaskSummaryResult parseLlmResponse(String llmResponse) {
        if (StringUtils.isEmpty(llmResponse)) {
            log.error("requestId: {} pattern matcher failed for response is null", requestId);
        }

        String[] parts1 = llmResponse.split("\\$\\$\\$");
        if (parts1.length < 2) {
            return TaskSummaryResult.builder().taskSummary(parts1[0]).build();
        }

        String summary = parts1[0];
        String fileNames = parts1[1];

        List<File> files = context.getProductFiles();
        if (!CollectionUtils.isEmpty(files)) {
            Collections.reverse(files);
        } else {
            log.error("requestId: {} llmResponse:{} productFile list is empty", requestId, llmResponse);
            // 文件列表为空，交付物中不显示文件
            return TaskSummaryResult.builder().taskSummary(summary).build();
        }
        List<File> product = new ArrayList<>();
        String[] items = fileNames.split("、");
        for (String item : items) {
            String trimmedItem = item.trim();
            if (StringUtils.isBlank(trimmedItem)) {
                continue;
            }
            for (File file : files) {
                if (item.contains(file.getFileName().trim())) {
                    log.info("requestId: {} add file:{}", requestId, file);
                    product.add(file);
                    break;
                }
            }
        }

        return TaskSummaryResult.builder().taskSummary(summary).files(product).build();
    }

    // 总结任务 - 修改后的主要方法
    public TaskSummaryResult summaryTaskResult(List<Message> messages, String query) {
        long startTime = System.currentTimeMillis();

        // 1. 参数校验
        if (CollectionUtils.isEmpty(messages) || StringUtils.isEmpty(query)) {
            return TaskSummaryResult.builder().taskSummary("").build();
        }

        try {
            // 2. 检查是否为银行数据分析报告生成任务
            if (isBankingAnalysisTask(query) && isReportGenerationTask(query)) {
                log.info("requestId: {} 检测到银行数据分析报告生成任务，调用BankDataReportTool", requestId);
                return handleBankingReportGeneration(messages, query);
            }

            // 3. 原有逻辑处理其他情况（包括银行数据查询）
            return handleNormalSummary(messages, query);

        } catch (Exception e) {
            log.error("requestId: {} in summaryTaskResult failed", requestId, e);
            return TaskSummaryResult.builder().taskSummary("任务执行失败，请联系管理员！").build();
        }
    }

    /**
     * 处理银行数据分析报告生成
     */
    private TaskSummaryResult handleBankingReportGeneration(List<Message> messages, String query) {
        try {
            // 创建BankDataReportTool实例
            BankDataReportTool bankReportTool = new BankDataReportTool();
            bankReportTool.setAgentContext(context);

            // 构建工具输入参数
            Map<String, Object> toolInput = new HashMap<>();
            toolInput.put("query", query);

            // 从执行历史中提取分析总结
            String analysis = extractAnalysisFromMessages(messages);
            toolInput.put("analysis", analysis);

            // 执行银行数据报告工具
            Object reportResult = bankReportTool.execute(toolInput);

            if (reportResult != null) {
                String reportContent = (String) reportResult;
                log.info("requestId: {} BankDataReportTool执行成功，生成报告长度: {}", requestId, reportContent.length());

                // 获取生成的文件
                List<File> generatedFiles = context.getProductFiles().stream()
                        .filter(file -> !file.getIsInternalFile() && file.getFileName().contains("银行数据分析报告"))
                        .collect(Collectors.toList());

                return TaskSummaryResult.builder()
                        .taskSummary("银行数据分析报告已成功生成，包含完整的数据分析、图表配置和业务建议。")
                        .files(generatedFiles)
                        .build();
            } else {
                log.error("requestId: {} BankDataReportTool执行失败", requestId);
                return TaskSummaryResult.builder()
                        .taskSummary("银行数据分析报告生成失败，请检查数据和配置。")
                        .build();
            }

        } catch (Exception e) {
            log.error("requestId: {} handleBankingReportGeneration failed", requestId, e);
            return TaskSummaryResult.builder()
                    .taskSummary("银行数据分析报告生成过程中发生错误，请联系管理员。")
                    .build();
        }
    }

    /**
     * 处理普通总结（包括银行数据查询）
     */
    private TaskSummaryResult handleNormalSummary(List<Message> messages, String query) {
        StringBuilder sb = new StringBuilder();
        for (Message message : messages) {
            String content = message.getContent();
            if (content != null && getMessageSizeLimit() != null && content.length() > getMessageSizeLimit()) {
                content = content.substring(0, getMessageSizeLimit());
            }
            sb.append(String.format("role:%s content:%s\n", message.getRole(), content));
        }

        String formattedPrompt = formatSystemPrompt(sb.toString(), query);
        Message userMessage = createSystemMessage(formattedPrompt);

        try {
            CompletableFuture<String> summaryFuture = getLlm().ask(
                    context,
                    Collections.singletonList(userMessage),
                    Collections.emptyList(),
                    false,
                    0.01);

            String llmResponse = summaryFuture.get();
            return parseLlmResponse(llmResponse);

        } catch (Exception e) {
            log.error("requestId: {} handleNormalSummary failed", requestId, e);
            return TaskSummaryResult.builder().taskSummary("任务执行失败，请联系管理员！").build();
        }
    }

    /**
     * 从消息历史中提取分析总结
     */
    private String extractAnalysisFromMessages(List<Message> messages) {
        StringBuilder analysis = new StringBuilder();

        for (Message message : messages) {
            String content = message.getContent();
            if (content != null && (content.contains("分析") || content.contains("总结") || content.contains("结果"))) {
                analysis.append(content).append("\n");
            }
        }

        if (analysis.length() == 0) {
            analysis.append("基于执行历史进行银行数据分析");
        }

        return analysis.toString();
    }

    /**
     * 判断是否为银行数据分析任务
     */
    private boolean isBankingAnalysisTask(String query) {
        return query.contains("银行") &&
                (query.contains("分析") || query.contains("归因分析") ||
                        query.contains("数据洞察") || query.contains("报告生成"));
    }

    /**
     * 判断是否为报告生成任务
     */
    private boolean isReportGenerationTask(String query) {
        return query.contains("报告生成") || query.contains("生成报告") ||
                query.contains("数据洞察") || query.contains("分析报告");
    }

    // 保留原有的银行分析专用方法，用于向后兼容
    public TaskSummaryResult summaryBankingTaskResult(List<Message> messages, String query, Map<String, Object> analysisData, boolean directOutput) {
        // 如果是报告生成任务，直接调用新的处理逻辑
        if (isReportGenerationTask(query)) {
            return handleBankingReportGeneration(messages, query);
        }

        // 否则使用原有逻辑
        return handleNormalSummary(messages, query);
    }
}