package dev.langchain4j.provider;

import com.suite.chatdatabi.common.pojo.ChatModelConfig;
import com.suite.chatdatabi.common.pojo.EmbeddingModelConfig;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;

public interface ModelFactory {
    ChatLanguageModel createChatModel(ChatModelConfig modelConfig);

    EmbeddingModel createEmbeddingModel(EmbeddingModelConfig embeddingModel);

    /**
     * 流式解析
     * 
     * @param modelConfig
     * @return
     */
    StreamingChatLanguageModel createStreamingChatModel(ChatModelConfig modelConfig);

}
