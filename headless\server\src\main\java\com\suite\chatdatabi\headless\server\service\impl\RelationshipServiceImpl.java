package com.suite.chatdatabi.headless.server.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.common.pojo.exception.InvalidArgumentException;
import com.suite.chatdatabi.common.pojo.exception.InvalidPermissionException;
import com.suite.chatdatabi.common.util.BeanMapper;
import com.suite.chatdatabi.common.util.PageUtils;
import com.suite.chatdatabi.headless.api.pojo.AppConfig;
import com.suite.chatdatabi.headless.api.pojo.MetaFilter;
import com.suite.chatdatabi.headless.api.pojo.enums.AppStatus;
import com.suite.chatdatabi.headless.api.pojo.request.AppQueryReq;
import com.suite.chatdatabi.headless.api.pojo.request.AppReq;
import com.suite.chatdatabi.headless.api.pojo.response.AppDetailResp;
import com.suite.chatdatabi.headless.api.pojo.response.AppResp;
import com.suite.chatdatabi.headless.api.pojo.response.DimensionResp;
import com.suite.chatdatabi.headless.api.pojo.response.MetricResp;
import com.suite.chatdatabi.headless.server.persistence.dataobject.AppDO;
import com.suite.chatdatabi.headless.server.persistence.dataobject.RelationshipDO;
import com.suite.chatdatabi.headless.server.persistence.mapper.AppMapper;
import com.suite.chatdatabi.headless.server.persistence.mapper.RelationshipMapper;
import com.suite.chatdatabi.headless.server.service.AppService;
import com.suite.chatdatabi.headless.server.service.DimensionService;
import com.suite.chatdatabi.headless.server.service.MetricService;
import com.suite.chatdatabi.headless.server.service.RelationshipService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RelationshipServiceImpl extends ServiceImpl<RelationshipMapper, RelationshipDO> implements RelationshipService {

    @Override
    public List<RelationshipDO> queryAllDataSetList(Set<Long> dataSetIds) {
        if (CollectionUtils.isEmpty(dataSetIds)){
            return new ArrayList<>();
        }
        return baseMapper.selectList(
                new LambdaQueryWrapper<RelationshipDO>()
                        .in(RelationshipDO::getDatasetId, dataSetIds)
        );
    }
}
