server:
  port: 9080
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

spring:
  profiles:
    active: ${S2_DB_TYPE:mysql}
    prompt:
      # yaml or db
      type: ${S2_PROMPT_TYPE:yaml}
      # dev or prod
      active: ${S2_PROMPT_ACTIVE:dev}
  application:
    name: chat
  config:
    import:
      - classpath:s2-config.yaml
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  ai:
    openai:
      api-key: ${AI_DASHSCOPE_API_KEY:sk-a05ffd35fae84a298473bcffaba11b85}
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
mybatis:
  mapper-locations=classpath:mappers/custom/*.xml,classpath*:/mappers/*.xml

logging:
  level:
    dev.langchain4j: DEBUG
    dev.ai4j.openai4j: DEBUG

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
  packages-to-scan: com.suite.chatdatabi
  paths-to-match: /api/chat/**,/api/semantic/**

knife4j:
  enable: true
  openapi:
    title: 'HChatData API Documentation'
    description: 'HChatData API Documentation'
    version: v1.0
  setting:
    language: zh-CN
#  basic:
#    enable: true
#    username: test
#    password: 123456#
  documents:
    default:
      title: ChatBI API Documents
      description: ChatBI API Documents