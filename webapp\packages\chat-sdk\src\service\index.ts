import axios from './axiosInstance';
import { getToken } from '../utils/utils';
import { AxiosRequestConfig } from 'axios';
import {
  ChatContextType,
  HistoryMsgItemType,
  HistoryType,
  MsgDataType,
  ParseDataType,
  SearchRecommendItem,
} from '../common/type';
import { isMobile } from '../utils/utils';
import { fetchEventSource } from '@microsoft/fetch-event-source';
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  skipGlobalAuth?: boolean;
}

const DEFAULT_CHAT_ID = 0;

const prefix = isMobile ? '/openapi' : '/api';

const token = getToken();

export function searchRecommend(
  queryText: string,
  chatId?: number,
  modelId?: number,
  agentId?: number
) {
  return axios.post<SearchRecommendItem[]>(`${prefix}/chat/query/search`, {
    queryText,
    chatId: chatId || DEFAULT_CHAT_ID,
    modelId,
    agentId,
  });
}

export function chatQuery(queryText: string, chatId?: number, modelId?: number, filters?: any[]) {
  return axios.post<MsgDataType>(`${prefix}/chat/query/query`, {
    queryText,
    chatId: chatId || DEFAULT_CHAT_ID,
    modelId,
    queryFilters: filters
      ? {
          filters,
        }
      : undefined,
  });
}

export function chatParse({
  queryText,
  chatId,
  modelId,
  agentId,
  parseId,
  queryId,
  filters,
  parseInfo,
}: {
  queryText: string;
  chatId?: number;
  modelId?: number;
  agentId?: number;
  queryId?: number;
  parseId?: number;
  filters?: any[];
  parseInfo?: ChatContextType;
}) {
  return axios.post<ParseDataType>(`${prefix}/chat/query/parse`, {
    queryText,
    chatId: chatId || DEFAULT_CHAT_ID,
    dataSetId: modelId,
    agentId,
    parseId,
    queryId,
    selectedParse: parseInfo,
    queryFilters: filters
      ? {
          filters,
        }
      : undefined,
  });
}

export function chatExecute(
  queryText: string,
  chatId: number,
  parseInfo: ChatContextType,
  agentId?: number
) {
  return axios.post<MsgDataType>(`${prefix}/chat/query/execute`, {
    queryText,
    agentId,
    chatId: chatId || DEFAULT_CHAT_ID,
    queryId: parseInfo.queryId,
    parseId: parseInfo.id,
  });
}

export function switchEntity(entityId: string, modelId?: number, chatId?: number) {
  return axios.post<any>(`${prefix}/chat/query/switchQuery`, {
    queryText: entityId,
    modelId,
    chatId: chatId || DEFAULT_CHAT_ID,
  });
}

export function queryData(chatContext: Partial<ChatContextType>) {
  return axios.post<MsgDataType>(`${prefix}/chat/query/queryData`, chatContext);
}

export function getHistoryMsg(
  current: number,
  chatId: number = DEFAULT_CHAT_ID,
  pageSize: number = 10
) {
  return axios.post<HistoryType>(`${prefix}/chat/manage/pageQueryInfo?chatId=${chatId}`, {
    current,
    pageSize,
  });
}

export function querySimilarQuestions(queryId: number) {
  return axios.get<HistoryMsgItemType>(`${prefix}/chat/manage/getChatQuery/${queryId}`);
}

export function deleteQuery(queryId: number) {
  return axios.delete<any>(`${prefix}/chat/manage/${queryId}`);
}

export function queryEntities(entityId: string | number, modelId: number) {
  return axios.post<any>(`${prefix}/chat/query/choice`, {
    entityId,
    modelId,
  });
}

export function updateQAFeedback(questionId: number, score: number) {
  return axios.post<any>(
    `${prefix}/chat/manage/updateQAFeedback?id=${questionId}&score=${score}&feedback=`
  );
}

export function queryDimensionValues(
  modelId: number,
  bizName: string,
  agentId: number,
  elementID: number,
  value: string
) {
  return axios.post<any>(`${prefix}/chat/query/queryDimensionValue`, {
    modelId,
    bizName,
    agentId,
    elementID,
    value,
  });
}

export function difyRequest(url: string, data: any, headers: Record<string, string> = {}) {
  return axios.post<any>(`${url}`,data,{
    headers: {
      'Content-Type': 'application/json', 
      ...headers   
    },
    skipGlobalAuth: true                       
  } as CustomAxiosRequestConfig);
}

// export function getDifyHistoryMsg(
//   url: string,
//   headers: Record<string, string> = {},
//   conversation_id: string,
//   user: string,
//   first_id: string,
//   limit: number = 10
// ) {
//   return axios.get<HistoryType>(`${url}`+ `?user=${user}&first_id=${first_id}&conversation_id=${conversation_id}&limit=${limit}`,{
//     headers,
//     skipGlobalAuth: true                       
//   } as CustomAxiosRequestConfig);
// }

export function saveDifyChat(data: any) {
  return axios.post<any>(
    `${prefix}/chat/query/saveDirectQA`,data
  );
}

type PartialMsgDataTypeWithAnswer = Partial<MsgDataType> & {
  answer?: string;
  conversation_id?: string;
};

// dify流式输出
export function chatExecuteStream(  
  url: string,
  params: any, 
  apikey: string,
  signal?: AbortSignal,
  onData?: (chunk: PartialMsgDataTypeWithAnswer) => void,  
  onError?: (error: Error) => void,  
  onComplete?: () => void,  
) {  
  let buffer = '';

  return fetch(`${url}`, {  
    method: 'POST', 
    signal: signal,  
    headers: {  
      'Content-Type': 'application/json',  
      'Accept': 'text/event-stream',  
      'Authorization': `Bearer ${apikey}`
    },  
    body: JSON.stringify({
      query: params.query,
      inputs: {},
      response_mode: "streaming",
      conversation_id: params.conversation_id,
      user: params.user,
      files: params.files || []
    }),
  }).then(response => { 
    if (!response.ok) {  
      throw new Error(`HTTP error! status: ${response.status}`);  
    }  
      
    const reader = response.body?.getReader();  
    const decoder = new TextDecoder();  

    function processEvents(dataStr: string) {
      // 处理多个事件（可能由\n\n分隔）
      const events = dataStr.split(/\n\n/).filter(e => e.trim());
      
      for (const eventData of events) {
        // 检查是否是SSE格式
        if (eventData.startsWith('data: ')) {
          const jsonStr = eventData.slice(6).trim();
          
          try {
            const data = JSON.parse(jsonStr);
            
            // 处理所有事件类型
            if (data.event === 'message' || data.event === 'agent_message') {
              // 过滤掉"ping"字符串
              const filteredAnswer = (data.answer || '').replace(/ping/g, '');
              onData?.({
                answer: filteredAnswer,  
                conversation_id: data.conversation_id   
              });
            } 
            // 处理工作流完成事件
            else if (data.event === 'workflow_finished' || data.event === 'message_end') {
              console.log('工作流完成:', data);
              onComplete?.(); 
            }
            // 处理节点事件
            else if (data.event === 'node_started' || data.event === 'node_finished') {
              // console.log(`节点 ${data.event}:`, data.data?.node_id);
            }
          } catch (e) {
            console.error('解析事件失败:', e, '原始数据:', jsonStr);
          }
        }
      }
    }
      
    function readStream() {  
      return reader?.read().then(({ done, value }) => {  
        if (done) {  
          // 处理剩余缓冲区数据
          if (buffer.trim()) processEvents(buffer);
          onComplete?.();  
          return;  
        }  

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true });

        // 尝试处理完整事件
        processEvents(buffer);

        // 清空已处理的数据（保留未完整事件）
        buffer = buffer.substring(buffer.lastIndexOf('\n\n') + 2);
        
        return readStream(); 
      });  
    }  
      
    return readStream();  
  }).catch(error => {  
    if (error.name === 'AbortError') {
      throw error;
    } else {
      onError?.(error);  
      throw error;
    } 
  });  
}

// 获取上下文历史
export function getContextHistory(
  queryText: string,
  agentId?: number,
  chatId?: number,
  queryId: number = 0,
  parseId: number = 0
) {
  return axios.post<any>(`${prefix}/chat/query/getHistoryInputs`, {
    queryText,
    agentId,
    chatId,
    queryId,
    parseId,
  });
}

// 原系统数据解读流式输出
export function dataParseStream(
  params: any, 
  signal?: AbortSignal,
  onData?: (chunk: PartialMsgDataTypeWithAnswer) => void,  
  onError?: (error: Error) => void,  
  onComplete?: () => void,  
) {
  let buffer = '';

  return fetch(`${prefix}/chat/query/summary`, {  
    method: 'POST', 
    signal: signal,  
    headers: {  
      'Content-Type': 'application/json',  
      'Accept': 'text/event-stream',  
      'Authorization': `Bearer ${token}`
    },  
    body: JSON.stringify({  
      queryText: params.queryText,
      agentId: params.agentId,
      chatId: params.chatId,
      queryId: params.queryId,
      parseId: params.parseId,
    }),
  }).then(response => { 
    if (!response.ok) {  
      throw new Error(`HTTP error! status: ${response.status}`);  
    }  
      
    const reader = response.body?.getReader();  
    const decoder = new TextDecoder();  

    function processEvents(dataStr: string) {
      // 处理多个事件（可能由\n\n分隔）
      const events = dataStr.split(/\n\n/).filter(e => e.trim());
      for (const eventData of events) {
        // 检查是否是SSE格式
        if (eventData.startsWith('data:')) {
          const jsonStr = eventData.slice(5).trim();
          try {
            const data = JSON.parse(jsonStr);
            
            // 处理所有事件类型
            // if (data.eventType === 'message' || data.eventType === 'agent_message') {
            //   // 过滤掉"ping"字符串
            //   const filteredAnswer = (data.data.answer || '').replace(/ping/g, '');
            //   onData?.({
            //     answer: filteredAnswer,  
            //   });
            // } 
            // 处理工作流完成事件
            if (data.eventType === 'workflow_finished') {
              onData?.({
                answer: data.data.answer,  
              });
              console.log('工作流完成:', data);
            }
            // 处理节点事件
            else if (data.eventType === 'node_started' || data.eventType === 'node_finished') {
              // console.log(`节点 ${data.event}:`, data.data?.node_id);
            }
          } catch (e) {
            console.error('解析事件失败:', e, '原始数据:', jsonStr);
          }
        }
      }
    }
      
    function readStream() {  
      return reader?.read().then(({ done, value }) => {  
        if (done) {  
          // 处理剩余缓冲区数据
          if (buffer.trim()) processEvents(buffer);
          onComplete?.();  
          return;  
        }  

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true });

        // 尝试处理完整事件
        processEvents(buffer);

        // 清空已处理的数据（保留未完整事件）
        buffer = buffer.substring(buffer.lastIndexOf('\n\n') + 2);
        
        return readStream(); 
      });  
    }  
      
    return readStream();  
  }).catch(error => {  
    if (error.name === 'AbortError') {
      throw error;
    } else {
      onError?.(error);  
      throw error;
    } 
  });    
}

// 使用@microsoft/fetch-event-source做流式输出
export function dataParseStream2(
  params: any,
  signal?: AbortSignal,
  onData?: (chunk: PartialMsgDataTypeWithAnswer) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
) {
  return fetchEventSource(`${prefix}/chat/query/summary`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify({
      queryText: params.queryText,
      agentId: params.agentId,
      chatId: params.chatId,
      queryId: params.queryId,
      parseId: params.parseId,
    }),
    signal: signal,
    async onopen(res) {
      if (res.ok && res.status === 200) {
        console.log("Connection made ", res);
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        // client-side errors are usually non-retriable:
        console.log("Client side error ", res);
        throw new Error(`HTTP error! status: ${res.status}`);
      }
    },
    onmessage(event) {
      // 处理SSE消息
      try {
        const data = JSON.parse(event.data);
        if (data.eventType === 'message') {
          onData?.({
            answer: data.data.answer,  
          });
        } 
        // 处理工作流完成事件
        else if (data.eventType === 'workflow_finished') {
          console.log('工作流完成:', data);
          onComplete?.();
        }
        // 处理节点事件
        else if (data.eventType === 'node_started' || data.eventType === 'node_finished') {
          // console.log(`节点 ${data.eventType}:`, data.data?.node_id);
        }
      } catch (e) {
        console.error('解析事件失败:', e, '原始数据:', event.data);
      }
    },
    onclose() {
      console.log("Connection closed by the server");
      onComplete?.();
    },
    onerror(err) {
      console.log("There was an error from server", err);
      if (err.name === 'AbortError') {
        throw err;
      } else {
        onError?.(err);
        throw err;
      }
    },
  });
}

export function queryDatabaseList(modelId: any) {  
  return axios.get<any>(`${prefix}/chat/query/queryDataBase?modelId=${modelId}`);
}

export function queryDataSetEnum(dataSetIds: any) {
  return axios.get(`${prefix}/chat/query/queryDataSet?dataSetId=${dataSetIds}`);
}

export function queryDomainId(modelId: any) {  
  return axios.get(`${prefix}/semantic/dataSet/${modelId}`);
}

export function getAllModelByDomainId(domainId: number): Promise<any> {
  return axios(`${process.env.API_BASE_URL}model/getAllModelByDomainId`, {
    method: 'GET',
    params: {
      domainId,
    },
  });
}

export function getDimensionList(data: any): Promise<any> {
  const { domainId, modelId } = data;
  const queryParams = {
    data: {
      current: 1,
      pageSize: 999999,
      ...data,
      ...(domainId ? { domainIds: [domainId] } : {}),
      ...(modelId ? { modelIds: [modelId] } : {}),
    },
  };
  return axios.post(`${process.env.API_BASE_URL}dimension/queryDimension`, queryParams.data);
}

export function queryMetric(data: any): Promise<any> {
  const { domainId, modelId } = data;
  const queryParams = {
    data: {
      current: 1,
      pageSize: 999999,
      ...data,
      ...(domainId ? { domainIds: [domainId] } : {}),
      ...(modelId ? { modelIds: [modelId] } : {}),
    },
  };
  return axios.post(`${process.env.API_BASE_URL}metric/queryMetric`, queryParams.data);
}

export function getDataSetDetail(id: any): Promise<any> {
  return axios(`${process.env.API_BASE_URL}dataSet/${id}`, {
    method: 'GET',
  });
}

export function getdataParse(params: any): Promise<any> {
  return axios.post(`${prefix}/chat/query/summaryAsync`,params);
}

export function difyUploadFiles(data: FormData) {
  return axios.post<any>(
    `https://***********:16401/v1/files/upload`,data,{
    headers: {
      'Authorization': `Bearer app-569eRk1NlWVv5BEL9I7rJenl`
    },
    skipGlobalAuth: true
  } as CustomAxiosRequestConfig);
}

// 归因分析流式接口
export function attributionParseStream(
  params: any,
  signal?: AbortSignal,
  onData?: (chunk: PartialMsgDataTypeWithAnswer) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
) {
  return fetchEventSource(`${prefix}/graph/attribution`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify({
      queryText: params.queryText,
      agentId: params.agentId,
      chatId: params.chatId,
    }),
    signal: signal,
    async onopen(res) {
      if (res.ok && res.status === 200) {
        console.log("Connection made ", res);
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        // client-side errors are usually non-retriable:
        console.log("Client side error ", res);
        throw new Error(`HTTP error! status: ${res.status}`);
      }
    },
    onmessage(event) {
      console.log('event-----',event);
      
      // 处理SSE消息
      try {
        const data = JSON.parse(event.data);
        if (data.hasOwnProperty('report_stream')) {
          onData?.({
            answer: data.report_stream,  
          });
        } 
        // 处理工作流完成事件
        else if (data.hasOwnProperty('__END__')) {
          console.log('工作流完成:', data);
          onComplete?.();
        }
        // 处理节点事件
        else if (data.hasOwnProperty('__START__')) {
          // console.log(`节点 ${data.eventType}:`, data.data?.node_id);
        }
      } catch (e) {
        console.error('解析事件失败:', e, '原始数据:', event.data);
      }
    },
    onclose() {
      console.log("Connection closed by the server");
      onComplete?.();
    },
    onerror(err) {
      console.log("There was an error from server", err);
      if (err.name === 'AbortError') {
        throw err;
      } else {
        onError?.(err);
        throw err;
      }
    },
  });
}
