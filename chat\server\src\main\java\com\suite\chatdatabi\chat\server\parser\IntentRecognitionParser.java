package com.suite.chatdatabi.chat.server.parser;

import com.suite.chatdatabi.chat.server.pojo.ParseContext;

import com.suite.chatdatabi.chat.server.service.IntentRecognitionService;
import com.suite.chatdatabi.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class IntentRecognitionParser implements ChatQueryParser {
    public boolean accept(ParseContext parseContext) {
        //ParseContext 中是否启用了 意图识别 功能
        if (parseContext.getAgent().enableIntentRecognition())
            log.info("使用的解析器 意图识别, parseContext:{}", parseContext.getAgent());
        return parseContext.getAgent().enableIntentRecognition();

    }

    @Override
    public void parse(ParseContext parseContext) {
        IntentRecognitionService intentService = ContextUtils.getBean(IntentRecognitionService.class);
        IntentRecognitionService.IntentType intentType =
                intentService.recognizeIntent(parseContext.getRequest().getQueryText(), parseContext.getAgent());

        if (intentType == null) {
            // 如果意图识别失败，默认使用数据查询
            parseContext.getAgent().setIntentType("数据查询");
            log.info("意图识别失败，默认使用数据查询");
            return;
        }

        switch (intentType) {
            case DIFY:
                parseContext.getAgent().setIntentType("DIFY");
                log.info("使用DIFY");
                break;
            case 数据查询:
                parseContext.getAgent().setIntentType("数据查询");
                log.info("使用数据查询");
                break;
            case 归因分析:
                parseContext.getAgent().setIntentType("归因分析");
                log.info("使用归因分析");
                break;
            case 生成报告:
                parseContext.getAgent().setIntentType("生成报告");
                log.info("使用生成报告");
                break;
            default:
                // 默认情况下使用数据查询
                parseContext.getAgent().setIntentType("数据查询");
                log.info("未知意图类型，默认使用数据查询");
                break;
        }
    }
}
