import {
  ChatContextType,
  DateInfoType,
  EntityInfoType,
  FilterItemType,
  MsgDataType,
  ParseStateEnum,
  ParseTimeCostType,
  RangeValue,
  SimilarQuestionType,
} from '../../common/type';
import type { MenuProps } from 'antd';
import { CheckCircleFilled, InfoCircleOutlined, MoreOutlined, ReadOutlined, Line<PERSON><PERSON>Outlined, Bar<PERSON><PERSON>Outlined, PieChartOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { ChartItemContext } from './context';
import { chatExecute, chatParse, queryData, deleteQuery, switchEntity, chatExecuteStream, saveDifyChat, getContextHistory, dataParseStream, getdataParse, dataParseStream2, attributionParseStream } from '../../service';
import { PARSE_ERROR_TIP, PREFIX_CLS, SEARCH_EXCEPTION_TIP, MsgContentTypeEnum } from '../../common/constants';
import { message, Spin, Dropdown, Button, Tooltip } from 'antd';
// import IconFont from '../IconFont';
import ExpandParseTip from './ExpandParseTip';
import ParseTip from './ParseTip';
import GenerateSqlTip from './GenerateSqlTip';
import IntentTip from './IntentTip';
import AttributionAnalysisTip from './AttributionAnalysisTip';
import ExecuteItem from './ExecuteItem';
import { isMobile, exportCsvFile } from '../../utils/utils';
import classNames from 'classnames';
import Tools from '../Tools';
// import SqlItem from './SqlItem';
import SimilarQuestionItem from './SimilarQuestionItem';
import { AgentType } from '../../Chat/type';
import dayjs, { Dayjs } from 'dayjs';
import { useMethodRegister } from '../../hooks';
import MarkDown from '../ChatMsg/MarkDown';
import { useStreaming } from '../../context/StreamingContext';
import Loading from './Loading'
// import AIBotIcon from '../../assets/AIBotIcon'
import avatar from '../../assets/ai-avator.png'
import SqlViewModel from './SqlViewModel'
import { onExportLog } from '../../utils/sqlViewUtils'

type Props = {
  msg: string;
  conversationId?: number;
  questionId?: number;
  modelId?: number;
  agentId?: number;
  score?: number;
  filter?: any[];
  parseInfos?: ChatContextType[];
  parseTimeCostValue?: ParseTimeCostType;
  msgData?: MsgDataType;
  triggerResize?: boolean;
  isDeveloper?: boolean;
  integrateSystem?: string;
  executeItemNode?: React.ReactNode;
  renderCustomExecuteNode?: boolean;
  isSimpleMode?: boolean;
  isDebugMode?: boolean;
  currentAgent?: AgentType;
  isLastMessage?: boolean;
  files?: any[];
  intentType?: string; // 新增：意图类型，用于页面刷新时显示意图分类组件
  difyParamsFromPageQuery?: {provider: string, baseUrl: string, apiKey: string, data_query_type?: string}; // 新增：从pageQueryInfo接口获取的difyParmes（注意后端字段名是difyParmes）
  onMsgDataLoaded?: (data: MsgDataType, valid: boolean, isRefresh?: boolean) => void;
  onUpdateMessageScroll?: () => void;
  onSendMsg?: (msg: string) => void;
};



const ChatItem: React.FC<Props> = ({
  msg,
  conversationId,
  questionId,
  modelId,
  agentId,
  score,
  filter,
  triggerResize,
  parseInfos,
  parseTimeCostValue,
  msgData,
  isDeveloper,
  integrateSystem,
  executeItemNode,
  renderCustomExecuteNode,
  isSimpleMode,
  currentAgent,
  isDebugMode,
  isLastMessage,
  files,
  intentType,
  difyParamsFromPageQuery,
  onMsgDataLoaded,
  onUpdateMessageScroll,
  onSendMsg,
}) => {
  const [parseLoading, setParseLoading] = useState(false);
  const [parseTimeCost, setParseTimeCost] = useState<ParseTimeCostType>();
  const [parseInfo, setParseInfo] = useState<ChatContextType>();
  const [parseInfoOptions, setParseInfoOptions] = useState<ChatContextType[]>([]);
  const [preParseInfoOptions, setPreParseInfoOptions] = useState<ChatContextType[]>([]);
  const [parseTip, setParseTip] = useState('');
  const [executeMode, setExecuteMode] = useState(false);
  const [preParseMode, setPreParseMode] = useState(false);
  const [showExpandParseTip, setShowExpandParseTip] = useState(false);
  const [executeLoading, setExecuteLoading] = useState(false);
  const [executeTip, setExecuteTip] = useState('');
  const [executeErrorMsg, setExecuteErrorMsg] = useState('');
  const [data, setData] = useState<MsgDataType>();
  const [entitySwitchLoading, setEntitySwitchLoading] = useState(false);
  const [dimensionFilters, setDimensionFilters] = useState<FilterItemType[]>([]);
  const [dateInfo, setDateInfo] = useState<DateInfoType>({} as DateInfoType);
  const [entityInfo, setEntityInfo] = useState<EntityInfoType>({} as EntityInfoType);
  const [dataCache, setDataCache] = useState<Record<number, { tip: string; data?: MsgDataType }>>(
    {}
  );
  const [forceChartType, setForceChartType] = useState<MsgContentTypeEnum | null>(null);
  const [isParserError, setIsParseError] = useState<boolean>(false);
  const [intentRecognitionError, setIntentRecognitionError] = useState<string>(''); // 意图分类错误信息

  const [difyParmes, setDifyParmes] = useState<{provider: string, baseUrl: string, apiKey: string, data_query_type?: string} | null>(null);
  const [streamingAnswer, setStreamingAnswer] = useState<string>('');  
  const [dataParseSAnswer, setdataParseSAnswer] = useState<string>('');  
  const [isStreaming, setIsStreaming] = useState<boolean>(false);  
  const [difyChatId,setDifyChatId] = useState('')
  const streamingAnswerRef = useRef('')
  const dataParseSAnswerRef = useRef('')
  const difyChatIdRef = useRef('')
  const { state: streamingState, dispatch: streamingDispatch } = useStreaming();
  const [copied, setCopied] = useState(false);
  const copyTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [sqlModalVisible,setSqlModalVisible] = useState<boolean>(false);
  const [dataParseLoading,setDataParseLoading] = useState<boolean>(false);
  const [parseData, setParseData] = useState<any>(null);
  const [intentRecognitionComplete, setIntentRecognitionComplete] = useState<boolean>(false);

  // 归因分析相关状态
  const [attributionAnalysisLoading, setAttributionAnalysisLoading] = useState<boolean>(false);
  const [attributionAnalysisData, setAttributionAnalysisData] = useState<string>('');
  const [attributionAnalysisFailed, setAttributionAnalysisFailed] = useState<boolean>(false);
  const [attributionAnalysisError, setAttributionAnalysisError] = useState<string>('');
  const [showAttributionAnalysis, setShowAttributionAnalysis] = useState<boolean>(false); // 控制归因分析组件显示时机
  const [isNewConversation, setIsNewConversation] = useState<boolean>(false); // 标识是否为新对话
  const attributionAnalysisDataRef = useRef('');

  // 思维链渲染状态
  const [thinkingChainStep, setThinkingChainStep] = useState<number>(3); // 默认显示所有组件
  const [enableThinkingChain, setEnableThinkingChain] = useState<boolean>(false); // 是否启用思维链动画
  const [thinkingChainStarted, setThinkingChainStarted] = useState<boolean>(false); // 思维链是否已经开始
  // 0: 只显示意图分类
  // 1: 显示意图分类 + 维度指标
  // 2: 显示意图分类 + 维度指标 + 生成SQL
  // 3: 显示意图分类 + 维度指标 + 生成SQL + 数据查询结果

  const copyContent = useMemo(() => {
    if (streamingAnswer) return streamingAnswer;
    if (data?.textResult) return data.textResult;
    return '';
  }, [streamingAnswer, data]);
  
  const resetState = () => {
    setParseLoading(false);
    setParseTimeCost(undefined);
    setParseInfo(undefined);
    setParseInfoOptions([]);
    setPreParseMode(false);
    setShowExpandParseTip(false);
    setPreParseInfoOptions([]);
    setParseTip('');
    setExecuteMode(false);
    setDimensionFilters([]);
    setData(undefined);
    setExecuteErrorMsg('');
    setDateInfo({} as DateInfoType);
    setEntityInfo({} as EntityInfoType);
    setDataCache({});
    setIsParseError(false);
    setIntentRecognitionComplete(false);
    setThinkingChainStep(3); // 重置为显示所有组件
    setEnableThinkingChain(false); // 重置思维链状态
    setThinkingChainStarted(false); // 重置思维链开始状态
    setIsNewConversation(false); // 重置新对话状态
    setShowAttributionAnalysis(false); // 重置归因分析显示状态
  };

  const prefixCls = `${PREFIX_CLS}-item`;

  // 启动思维链渲染 - 只有新消息才启用
  const startThinkingChain = () => {
    // 防止重复执行思维链
    if (thinkingChainStarted) {
      return;
    }

    // 只有在启用思维链的情况下才执行动画
    if (!enableThinkingChain) {
      setThinkingChainStep(3); // 直接显示所有组件
      return;
    }

    setThinkingChainStarted(true); // 标记思维链已开始
    setThinkingChainStep(0);

    // 1秒后显示维度指标组件
    setTimeout(() => {
      setThinkingChainStep(1);
      // 触发滚动更新
      if (onUpdateMessageScroll) {
        requestAnimationFrame(() => {
          onUpdateMessageScroll();
        });
      }
    }, 1000);

    // 2秒后显示生成SQL组件
    setTimeout(() => {
      setThinkingChainStep(2);
      // 触发滚动更新
      if (onUpdateMessageScroll) {
        requestAnimationFrame(() => {
          onUpdateMessageScroll();
        });
      }
    }, 2000);

    // 3秒后显示数据查询结果组件
    setTimeout(() => {
      setThinkingChainStep(3);
      // 触发滚动更新
      if (onUpdateMessageScroll) {
        requestAnimationFrame(() => {
          onUpdateMessageScroll();
        });
      }
    }, 3000);
  };

  // 计算当前应该显示的组件步骤
  const getComponentStep = (componentType: 'parse' | 'sql' | 'execute') => {
    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
    const hasIntentRecognitionError = !!intentRecognitionError;
    const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';

    // 如果是意图分类失败或老数据，不显示思维链
    if (hasIntentRecognitionError || isOldDataWithoutDifyParmes) {
      return false;
    }

    // 如果不启用思维链，直接根据组件类型判断是否显示
    if (!enableThinkingChain) {
      switch (componentType) {
        case 'parse':
          return !preParseMode && parseInfoOptions.length > 0;
        case 'sql':
          return !preParseMode && parseInfo?.sqlInfo && !isSimpleMode;
        case 'execute':
          return executeMode;
        default:
          return false;
      }
    }

    // 启用思维链时，根据步骤判断
    let stepCounter = 0;

    // 维度指标组件
    const shouldShowParseTip = !preParseMode && parseInfoOptions.length > 0;
    if (componentType === 'parse') {
      return shouldShowParseTip && thinkingChainStep >= ++stepCounter;
    }
    if (shouldShowParseTip) stepCounter++;

    // 生成SQL组件
    const shouldShowSqlTip = !preParseMode && parseInfo?.sqlInfo && !isSimpleMode;
    if (componentType === 'sql') {
      return shouldShowSqlTip && thinkingChainStep >= ++stepCounter;
    }
    if (shouldShowSqlTip) stepCounter++;

    // 数据查询结果组件
    if (componentType === 'execute') {
      return executeMode && thinkingChainStep >= ++stepCounter;
    }

    return false;
  };

  const updateData = (res: Result<MsgDataType>) => {
    let tip: string = '';
    let data: MsgDataType | undefined = undefined;
    const { queryColumns, queryResults, queryState, queryMode, response, chatContext, errorMsg } =
      res.data || {};
    setExecuteErrorMsg(errorMsg);
    if (res.code === 400 || res.code === 401 || res.code === 412) {
      tip = res.msg;
    } else if (res.code !== 200) {
      tip = SEARCH_EXCEPTION_TIP;
    } else if (queryState !== 'SUCCESS') {
      tip = response && typeof response === 'string' ? response : SEARCH_EXCEPTION_TIP;
    } else if (
      (queryColumns && queryColumns.length > 0 && queryResults) ||
      queryMode === 'WEB_PAGE' ||
      queryMode === 'WEB_SERVICE' ||
      queryMode === 'PLAIN_TEXT'
    ) {
      data = res.data;
      tip = '';
    }
    if (chatContext) {
      setDataCache({ ...dataCache, [chatContext!.id!]: { tip, data } });
    }
    if (data) {
      setData(data);
      setExecuteTip('');
      return true;
    }
    setExecuteTip(tip || SEARCH_EXCEPTION_TIP);
    return false;
  };

  const onExecute = async (
    parseInfoValue: ChatContextType,
    parseInfos?: ChatContextType[],
    isSwitchParseInfo?: boolean,
    isRefresh = false
  ) => {
    setExecuteMode(true);
    if (isSwitchParseInfo) {
      setEntitySwitchLoading(true);
    } else {
      setExecuteLoading(true);
    }
    try {
      const res: any = await chatExecute(msg, conversationId!, parseInfoValue, agentId);
      const valid = updateData(res);
      onMsgDataLoaded?.(
        {
          ...res.data,
          parseInfos,
          queryId: parseInfoValue.queryId,
        },
        valid,
        isRefresh
      );
    } catch (e) {
      const tip = SEARCH_EXCEPTION_TIP;
      setExecuteTip(SEARCH_EXCEPTION_TIP);
      setDataCache({ ...dataCache, [parseInfoValue!.id!]: { tip } });
    }
    if (isSwitchParseInfo) {
      setEntitySwitchLoading(false);
    } else {
      setExecuteLoading(false);
    }
  };

  const updateDimensionFitlers = (filters: FilterItemType[]) => {
    setDimensionFilters(
      filters.sort((a, b) => {
        if (a.name < b.name) {
          return -1;
        }
        if (a.name > b.name) {
          return 1;
        }
        return 0;
      })
    );
  };

  const sendMsg = async () => {
    setParseLoading(true);
    setIntentRecognitionError(''); // 重置意图分类错误信息
    setEnableThinkingChain(true); // 新消息启用思维链
    setThinkingChainStarted(false); // 重置思维链开始状态
    setThinkingChainStep(0); // 从步骤0开始
    setIsNewConversation(true); // 标记为新对话

    try {
      const parseData: any = await chatParse({
        queryText: msg,
        chatId: conversationId,
        modelId,
        agentId,
        filters: filter,
      });
      setParseLoading(false);
      const { code, data } = parseData || {};
      const { state, selectedParses, candidateParses, queryId, parseTimeCost, errorMsg, difyParmes } = data || {};

      if (difyParmes) {
        setDifyParmes(difyParmes);
      }

      // 处理归因分析
      if (difyParmes?.data_query_type === "归因分析") {
        await handleAttributionAnalysisStream(difyParmes);
        return;
      }

      if (difyParmes?.provider === "DIFY" && !difyParmes?.data_query_type) {
        const difyHistory = await handleContext();
        await handleDifyStream(difyParmes, difyHistory);
      }

      const parses = (selectedParses || []).concat(candidateParses || []);
      if (
        code !== 200 ||
        state === ParseStateEnum.FAILED ||
        !parses ||
        !Array.isArray(parses) ||
        !parses.length ||
        (!parses[0]?.properties?.type && !parses[0]?.queryMode)
      ) {
        // 接口报错或解析失败，设置意图分类错误信息
        const errorMessage = state === ParseStateEnum.FAILED && errorMsg ? errorMsg : PARSE_ERROR_TIP;
        setIntentRecognitionError(errorMessage);
        setParseTip(errorMessage);
        setParseInfo({ queryId } as any);
        return;
      }

      // 解析成功，继续处理
      onUpdateMessageScroll?.();
      const parseInfos = parses.slice(0, 5).map((item: any) => ({
        ...item,
        queryId,
      }));
      if (parseInfos.length > 1) {
        setPreParseInfoOptions(parseInfos);
        setShowExpandParseTip(true);
        setPreParseMode(true);
      }
      setParseInfoOptions(parseInfos || []);
      const parseInfoValue = parseInfos[0];
      if (!(currentAgent?.enableFeedback === 1 && parseInfos.length > 1)) {
        setParseInfo(parseInfoValue);
      }
      setParseTimeCost(parseTimeCost);
      setEntityInfo(parseInfoValue.entityInfo || {});
      updateDimensionFitlers(parseInfoValue?.dimensionFilters || []);
      setDateInfo(parseInfoValue?.dateInfo);

      if (parseInfos.length === 1) {
        onExecute(parseInfoValue, parseInfos);
      }
    } catch (error) {
      // 接口请求异常，设置意图分类错误信息
      setParseLoading(false);
      const errorMessage = '网络请求失败，请重试';
      setIntentRecognitionError(errorMessage);
      setParseTip(errorMessage);
      console.error('chatParse接口请求失败:', error);
      return;
    }
  };

  const createMockData = (
    question: string,
    answer: string,
    parseInfos: any,
  ): any => {
    return {
      id: Date.now(),
      question,
      chatContext: null,
      entityInfo: null,
      queryColumns: [],
      queryResults: [],
      queryId: null,
      queryMode: 'PLAIN_TEXT',
      queryState: 'SUCCESS',
      queryText: question,
      response: answer,
      parseInfos: parseInfos,
      queryTimeCost: 0,
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: '',
      textResult: answer,
      textSummary: answer
    };
  };

  const saveChatRecord = async (
    conversationId: number | undefined,
    agentId: number | undefined,
    question: string,
    answer: string,
  ) => {
    try {
      await saveDifyChat({
        chatId: conversationId,
        agentId: agentId,
        parseId: 0,
        queryText: question,
        answer: answer,
      });
      return true;
    } catch (error) {
      console.error('保存聊天记录失败:', error);
      return false;
    }
  };

  const handleStreamComplete = async (finalAnswer: string) => {
    setIsStreaming(false);
    setExecuteLoading(false);
    
    // 保存聊天记录
    const saveSuccess = await saveChatRecord(
      conversationId,
      agentId,
      msg,
      finalAnswer,
    );
    
    if (saveSuccess) {
      // 更新UI状态
      const mockData = createMockData(msg, finalAnswer, parseInfos,);
      setData(mockData);
      onMsgDataLoaded?.(mockData, true, false);
    } else {
      setExecuteTip('保存聊天记录失败');
    }
    
    // 重置流式状态
    if (streamingState.isStreaming) {
      streamingDispatch({ type: 'RESET' });
    }
  };

  const handleContext = async() => {
    const contextRes = await getContextHistory(msg, agentId || currentAgent?.id, conversationId, 0, 0)
    return contextRes
  }

  // 处理意图分类完成后的逻辑
  const handleIntentComplete = () => {
    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;

    // 如果是归因分析类型
    if (currentDifyParmes?.data_query_type === "归因分析") {
      if (isNewConversation) {
        // 新对话：延迟1秒后显示归因分析组件
        setTimeout(() => {
          setShowAttributionAnalysis(true);
        }, 1000);
      } else {
        // 页面刷新或切换智能体：立即显示归因分析组件
        setShowAttributionAnalysis(true);
      }
    }
  }

  const handleAttributionAnalysisStream = async (difyParmes: any) => {
    setAttributionAnalysisLoading(true);
    setAttributionAnalysisFailed(false);
    setAttributionAnalysisError('');
    setAttributionAnalysisData('');
    attributionAnalysisDataRef.current = '';
    setIsStreaming(true);
    setExecuteMode(true);

    const streamParams = {
      queryText: msg,
      agentId: agentId || currentAgent?.id,
      chatId: conversationId,
    };

    const controller = new AbortController();

    // 通知开始流式输出
    streamingDispatch({
      type: 'START_STREAMING',
      controller,
      context: {
        conversationId,
        agentId,
        question: msg,
        parseInfos: parseInfos || []
      }
    });

    try {
      await attributionParseStream(
        streamParams,
        controller.signal,
        // onData callback - 处理流式数据
        (chunk) => {
          if (chunk.answer) {
            console.log('answer', chunk.answer);
            
            setAttributionAnalysisLoading(false);
            setAttributionAnalysisData(prev => {
              const newValue = prev + chunk.answer;
              attributionAnalysisDataRef.current = newValue;
              if (onUpdateMessageScroll) {
                // 使用 requestAnimationFrame 确保在 DOM 更新后执行
                requestAnimationFrame(() => {
                  onUpdateMessageScroll();
                });
              }
              return newValue;
            });
          }
        },
        // onError callback
        (error) => {
          console.error('Attribution analysis stream error:', error);
          setAttributionAnalysisFailed(true);
          setAttributionAnalysisError(error.message || '归因分析出错');
          setAttributionAnalysisLoading(false);
          setIsStreaming(false);
          streamingDispatch({ type: 'RESET' });
        },
        // onComplete callback
        () => {
          console.log('Attribution analysis stream completed');
          setAttributionAnalysisLoading(false);
          setIsStreaming(false);
          streamingDispatch({ type: 'STOP_STREAMING' });
        }
      );
    } catch (error: any) {
      console.error('Attribution analysis error:', error);
      setAttributionAnalysisFailed(true);
      setAttributionAnalysisError(error.message || '归因分析失败');
      setAttributionAnalysisLoading(false);
      setIsStreaming(false);
      streamingDispatch({ type: 'RESET' });
    }
  }

  const handleDifyStream = async (difyParmes, difyHistory) => {  
    if (!difyParmes || difyParmes.provider !== "DIFY") {  
      return;  
    }  
      
    setExecuteMode(true);  
    setExecuteLoading(true);  
    setIsStreaming(true);  
    setStreamingAnswer('');  
    streamingAnswerRef.current = '';
    difyChatIdRef.current = '';

    const newQuery = {
      queryText: msg,
      history: difyHistory || '',
    }
      
    const streamParams = {
      query: JSON.stringify(newQuery),
      // query: msg,
      conversation_id: '',
      user: localStorage.getItem('user'),
      files: files || []
    };

    const controller = new AbortController();

    // 通知开始流式输出
    streamingDispatch({
      type: 'START_STREAMING',
      controller,
      context: {
        conversationId,
        agentId,
        question: msg,
        parseInfos: parseInfos || []
      }
    });
      
    try {  
      await chatExecuteStream(  
        difyParmes.baseUrl,  
        streamParams,  
        difyParmes.apiKey,  
        controller.signal,
        // onData callback - 处理流式数据  
        (chunk) => {  
          if (chunk.answer) {  
            setExecuteLoading(false); 
            setStreamingAnswer(prev => {
              const newValue = prev + chunk.answer;
              streamingAnswerRef.current = newValue; 
              if (onUpdateMessageScroll) {
                // 使用 requestAnimationFrame 确保在 DOM 更新后执行
                requestAnimationFrame(() => {
                  onUpdateMessageScroll();
                });
              }
              return newValue;
            });  
          }  
          // 更新 conversation_id 
          if (chunk.conversation_id) {  
            difyChatIdRef.current = chunk.conversation_id;
            setDifyChatId(chunk.conversation_id)
          }  
        },  
        // onError callback  
        (error) => {  
          console.error('Dify stream error:', error);  
          setExecuteTip(error.message || '流式对话出错');  
          setIsStreaming(false);  
          setExecuteLoading(false);  
        },  
        // onComplete callback  
        async () => {  
          setIsStreaming(false);  
          setExecuteLoading(false);  
          streamingDispatch({ type: 'STOP_STREAMING' });

          // 从ref获取最新值（确保是最新的）
          const finalAnswer = streamingAnswerRef.current;
          await handleStreamComplete(finalAnswer);
        },
      );  
    } catch (error: any) {  
      // 停止流式后，保存对话历史
      if (error.name === 'AbortError') {
        const partialAnswer = streamingAnswerRef.current;
        await handleStreamComplete(partialAnswer);
      }else{
        setExecuteTip('流式对话失败');
        setIsStreaming(false);
        setExecuteLoading(false);
        streamingDispatch({ type: 'RESET' });
      }
    } finally {
      // 流式结束后重置状态
      if (streamingState.isStreaming) {
        streamingDispatch({ type: 'RESET' });
      }
      setIsStreaming(false);
      setExecuteLoading(false);
    }  
  };

  useEffect(() => {
    return () => {
      // 组件卸载时停止流式输出
      if (streamingState.isStreaming) {
        streamingDispatch({ type: 'STOP_STREAMING' });
      }
    };
  }, []);

  const initChatItem = (msg, msgData) => {
    if (msgData) {
      // 页面刷新或切换智能体时，标记为非新对话
      setIsNewConversation(false);

      const parseInfoOptionsValue =
        parseInfos && parseInfos.length > 0
          ? parseInfos.map(item => ({ ...item, queryId: msgData.queryId }))
          : [{ ...msgData.chatContext, queryId: msgData.queryId }];
      const parseInfoValue = parseInfoOptionsValue[0];
      setParseInfoOptions(parseInfoOptionsValue);
      setParseInfo(parseInfoValue);
      setParseTimeCost(parseTimeCostValue);
      updateDimensionFitlers(parseInfoValue.dimensionFilters || []);
      setDateInfo(parseInfoValue.dateInfo);
      setExecuteMode(true);
      updateData({ code: 200, data: msgData, msg: 'success' });


      if (difyParamsFromPageQuery && !difyParmes) {
        setDifyParmes(difyParamsFromPageQuery);
      }

      // 如果有data_query_type或intentType，设置意图分类完成状态
      const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
      if (currentDifyParmes?.data_query_type || intentType) {
        setIntentRecognitionComplete(true);
        // 页面刷新时，不启用思维链，直接显示所有组件
        setEnableThinkingChain(false);
        setThinkingChainStarted(true); // 标记为已完成，防止后续启动
        setThinkingChainStep(3);

        // 如果是归因分析类型，设置静态数据并立即显示归因分析组件
        if (currentDifyParmes?.data_query_type === "归因分析") {
          // 如果有静态数据，设置它
          if (msgData.textResult) {
            setAttributionAnalysisData(msgData.textResult);
          }
          // 页面刷新时立即显示归因分析组件（与意图分类同时显示）
          setShowAttributionAnalysis(true);
        }
      }
    } else if (msg) {
      sendMsg();
    }
  };

  useEffect(() => {
    if (data !== undefined || executeTip !== '' || parseLoading) {
      return;
    }
    initChatItem(msg, msgData);
  }, [msg, msgData]);

  const onSwitchEntity = async (entityId: string) => {
    setEntitySwitchLoading(true);
    const res = await switchEntity(entityId, data?.chatContext?.modelId, conversationId || 0);
    setEntitySwitchLoading(false);
    setData(res.data);
    const { chatContext, entityInfo } = res.data || {};
    const chatContextValue = { ...(chatContext || {}), queryId: parseInfo?.queryId };
    setParseInfo(chatContextValue);
    setEntityInfo(entityInfo);
    updateDimensionFitlers(chatContextValue?.dimensionFilters || []);
    setDateInfo(chatContextValue?.dateInfo);
    setDataCache({ ...dataCache, [chatContextValue.id!]: { tip: '', data: res.data } });
  };

  const onFiltersChange = (dimensionFilters: FilterItemType[]) => {
    setDimensionFilters(dimensionFilters);
  };

  const onDateInfoChange = (dates: [Dayjs | null, Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      const [start, end] = dates;
      setDateInfo({
        ...(dateInfo || {}),
        startDate: dayjs(start).format('YYYY-MM-DD'),
        endDate: dayjs(end).format('YYYY-MM-DD'),
        dateMode: 'BETWEEN',
        unit: 0,
      });
    }
  };

  const handlePresetClick = (range: RangeValue) => {
    setDateInfo({
      ...(dateInfo || {}),
      startDate: dayjs(range[0]).format('YYYY-MM-DD'),
      endDate: dayjs(range[1]).format('YYYY-MM-DD'),
      dateMode: 'BETWEEN',
      unit: 0,
    });
  };

  const onRefresh = async (parseInfoValue?: ChatContextType) => {
    setEntitySwitchLoading(true);
    const { dimensions, metrics, id, queryId } = parseInfoValue || parseInfo || {};
    const chatContextValue = {
      dimensions,
      metrics,
      dateInfo,
      dimensionFilters,
      parseId: id,
      queryId,
    };
    const res: any = await queryData(chatContextValue);
    setEntitySwitchLoading(false);
    if (res.code === 200) {
      const resChatContext = res.data?.chatContext;
      const contextValue = { ...(resChatContext || chatContextValue), queryId };
      const dataValue = {
        ...res.data,
        chatContext: contextValue,
        parseInfos: parseInfoOptions,
        queryId,
      };
      onMsgDataLoaded?.(dataValue, true, true);
      setData(dataValue);
      setParseInfo(contextValue);
      setDataCache({ ...dataCache, [id!]: { tip: '', data: dataValue } });
    }
  };

  const deleteQueryInfo = async (queryId: number) => {
    const { code }: any = await deleteQuery(queryId);
    if (code === 200) {
      resetState();
      initChatItem(msg, undefined);
    }
  };

  const onSelectParseInfo = async (parseInfoValue: ChatContextType) => {
    setParseInfo(parseInfoValue);
    updateDimensionFitlers(parseInfoValue.dimensionFilters || []);
    setDateInfo(parseInfoValue.dateInfo);
    if (parseInfoValue.entityInfo) {
      setEntityInfo(parseInfoValue.entityInfo);
    }

    // 切换解析信息时不启用思维链，直接显示所有组件
    setEnableThinkingChain(false);
    setThinkingChainStarted(true); // 标记为已完成，防止后续启动
    setThinkingChainStep(3);

    if (dataCache[parseInfoValue.id!]) {
      const { tip, data } = dataCache[parseInfoValue.id!];
      setExecuteTip(tip);
      setData(data);
      onMsgDataLoaded?.(
        {
          ...(data as any),
          parseInfos,
          queryId: parseInfoValue.queryId,
        },
        true,
        true
      );
    } else {
      onExecute(parseInfoValue, parseInfoOptions, true);
    }
  };

  const onExpandSelectParseInfo = async (parseInfoValue: ChatContextType) => {
    setParseInfo(parseInfoValue);
    setPreParseMode(false);
    const { id: parseId, queryId } = parseInfoValue;
    setParseLoading(true);
    const { code, data }: any = await chatParse({
      queryText: msg,
      chatId: conversationId,
      modelId,
      agentId,
      filters: filter,
      parseId,
      queryId,
      parseInfo: parseInfoValue,
    });
    setParseLoading(false);
    if (code === 200) {
      setParseTimeCost(data.parseTimeCost);
      const parseInfo = data.selectedParses[0];
      parseInfo.queryId = data.queryId;
      setParseInfoOptions([parseInfo]);
      setParseInfo(parseInfo);
      updateDimensionFitlers(parseInfo.dimensionFilters || []);
      setDateInfo(parseInfo.dateInfo);
      if (parseInfo.entityInfo) {
        setEntityInfo(parseInfo.entityInfo);
      }
      onExecute(parseInfo, [parseInfo], true, true);
    }
  };

  const onExportData = () => {
    const { queryColumns, queryResults } = data || {};
    if (!!queryResults) {
      const exportData = queryResults.map(item => {
        return Object.keys(item).reduce((result, key) => {
          const columnName = queryColumns?.find(column => column.nameEn === key)?.name || key;
          result[columnName] = item[key];
          return result;
        }, {});
      });
      exportCsvFile(exportData);
    }
  };

  const onSelectQuestion = (question: SimilarQuestionType) => {
    onSendMsg?.(question.queryText);
  };

  const contentClass = classNames(`${prefixCls}-content`, {
    [`${prefixCls}-content-mobile`]: isMobile,
  });

  const { llmReq, llmResp } = parseInfo?.properties?.CONTEXT || {};

  const { register, call } = useMethodRegister(() => message.error('该条消息暂不支持该操作'));

  const handleCopy = useCallback(() => {
    if (copied || !copyContent) return;
    
    if (navigator.clipboard) {
      navigator.clipboard.writeText(copyContent)
        .then(() => handleCopySuccess())
        .catch(handleCopyError);
    } 
    // 兼容旧浏览器
    else {
      legacyCopy(copyContent);
    }
  }, [copied, copyContent]);
  
  const handleCopySuccess = () => {
    setCopied(true);
    message.success('复制成功');
    
    // 1秒后重置状态
    if (copyTimerRef.current) clearTimeout(copyTimerRef.current);
    copyTimerRef.current = setTimeout(() => setCopied(false), 1000);
  };
  
  const handleCopyError = (error: Error) => {
    message.error('复制失败');
  };
  
  const legacyCopy = (content: string) => {
    const textArea = document.createElement('textarea');
    textArea.value = content;
    textArea.style.position = 'fixed';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        handleCopySuccess();
      } else {
        handleCopyError(new Error('execCommand failed'));
      }
    } catch (err) {
      handleCopyError(err as Error);
    } finally {
      document.body.removeChild(textArea);
    }
  };

  // 导出日志
  const onExportLogFn = () => {
    if(parseInfo){
      onExportLog(
        msg,
        llmReq,
        llmResp,
        parseInfo.sqlInfo,
        executeErrorMsg,
        agentId,
        parseInfo.queryId,
        'LLM_S2SQL'
      )
    }
  }

  // 获取所有图表类型及其可用状态
  const getAllChartTypesWithStatus = () => {
    if (!data?.queryColumns || !data?.queryResults) {
      return [];
    }

    const columns = data.queryColumns;
    const dataSource = data.queryResults;
    const dateField = columns.find(item => item.showType === 'DATE' || item.type === 'DATE');
    const categoryField = columns.filter(item => item.showType === 'CATEGORY');
    const metricFields = columns.filter(item => item.showType === 'NUMBER');

    // 定义所有图表类型
    const allChartTypes = [
      { key: MsgContentTypeEnum.METRIC_TREND, label: '折线图', icon: <LineChartOutlined /> },
      { key: MsgContentTypeEnum.METRIC_BAR, label: '柱状图', icon: <BarChartOutlined /> },
      { key: MsgContentTypeEnum.METRIC_PIE, label: '饼状图', icon: <PieChartOutlined /> }
    ];

    // 检查每种图表类型的可用性
    return allChartTypes.map(chartType => {
      let isAvailable = false;
      let reason = '';

      switch (chartType.key) {
        case MsgContentTypeEnum.METRIC_TREND:
          // 折线图条件：有时间字段和指标字段，分类字段不超过1个
          isAvailable = !!(dateField && metricFields.length > 0 && categoryField.length <= 1 &&
            !dataSource.every(item => item[dateField.bizName] === dataSource[0][dateField.bizName]));
          if (!isAvailable) {
            if (!dateField) reason = '需要时间字段';
            else if (metricFields.length === 0) reason = '需要数值字段';
            else if (categoryField.length > 1) reason = '分类字段过多';
            else reason = '时间数据无变化';
          }
          break;

        case MsgContentTypeEnum.METRIC_BAR:
          // 柱状图条件：有分类字段和指标字段
          isAvailable = !!(categoryField.length > 0 && metricFields.length === 1 &&
            (isMobile ? dataSource.length <= 5 : dataSource.length <= 50));
          if (!isAvailable) {
            if (categoryField.length === 0) reason = '需要分类字段';
            else if (metricFields.length !== 1) reason = '需要1个数值字段';
            else reason = '数据量过大';
          }
          break;

        case MsgContentTypeEnum.METRIC_PIE:
          // 饼图条件：有1个分类字段和1个指标字段，无时间字段，数据量适中
          isAvailable = !!(categoryField.length === 1 && metricFields.length === 1 &&
            !dateField && dataSource.length > 1 && dataSource.length <= 20);
          if (!isAvailable) {
            if (categoryField.length !== 1) reason = '需要1个分类字段';
            else if (metricFields.length !== 1) reason = '需要1个数值字段';
            else if (dateField) reason = '不支持时间数据';
            else if (dataSource.length <= 1) reason = '数据量不足';
            else reason = '数据量过大';
          }
          break;
      }

      return {
        ...chartType,
        isAvailable,
        reason
      };
    });
  };

  // 获取当前图表类型
  const getCurrentChartType = () => {
    if (forceChartType) return forceChartType;

    if (!data?.queryColumns || !data?.queryResults) {
      return null;
    }

    const columns = data.queryColumns;
    const dataSource = data.queryResults;
    const dateField = columns.find(item => item.showType === 'DATE' || item.type === 'DATE');
    const categoryField = columns.filter(item => item.showType === 'CATEGORY');
    const metricFields = columns.filter(item => item.showType === 'NUMBER');

    // 使用与ChatMsg组件相同的逻辑
    const isMetricTrend =
      dateField &&
      metricFields.length > 0 &&
      categoryField.length <= 1 &&
      !(metricFields.length > 1 && categoryField.length > 0) &&
      !dataSource.every(item => item[dateField.bizName] === dataSource[0][dateField.bizName]);

    if (isMetricTrend) {
      return MsgContentTypeEnum.METRIC_TREND;
    }

    const isMetricBar =
      categoryField?.length > 0 &&
      metricFields?.length === 1 &&
      (isMobile ? dataSource?.length <= 5 : dataSource?.length <= 50);

    if (isMetricBar) {
      return MsgContentTypeEnum.METRIC_BAR;
    }

    const isMetricPie =
      categoryField?.length === 1 &&
      metricFields?.length === 1 &&
      !dateField &&
      dataSource?.length > 1 &&
      dataSource?.length <= 20;

    if (isMetricPie) {
      return MsgContentTypeEnum.METRIC_PIE;
    }

    return null;
  };

  const allChartTypes = getAllChartTypesWithStatus();
  const currentChartType = getCurrentChartType();

  // 图表切换菜单项 - 直接的菜单项列表
  const chartSwitchMenuItems: MenuProps['items'] = allChartTypes.map(chartType => ({
    key: chartType.key,
    disabled: !chartType.isAvailable,
    label: (
      <span style={{
        fontWeight: chartType.key === currentChartType ? 'bold' : 'normal',
        color: chartType.key === currentChartType ? '#1890ff' :
               !chartType.isAvailable ? '#d9d9d9' : 'inherit',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <span style={{
            color: chartType.key === currentChartType ? '#1890ff' :
                   !chartType.isAvailable ? '#d9d9d9' : 'inherit'
          }}>
            {chartType.icon}
          </span>
          {chartType.label}
        </span>
        {!chartType.isAvailable && (
          <span style={{
            fontSize: '12px',
            color: '#999',
            marginLeft: '8px'
          }}>
            {chartType.reason}
          </span>
        )}
      </span>
    ),
  }));

  const moreItems: MenuProps['items'] = [
    {
      label: '导出数据',
      key: '0',
    },
    // {
    //   label: '查看SQL',
    //   key: '1',
    // },
    {
      label: '导出日志',
      key: '3',
    },
  ];
  
  const moretMenuClick: MenuProps['onClick'] = ({ key }) => {
    if(key === '0') {
      onExportData();
    }
    // else if(key === '1') {
    //   setSqlModalVisible(true);
    // }
    else if(key === '3') {
      onExportLogFn();
    }
  }

  // 图表切换菜单点击处理
  const chartSwitchMenuClick: MenuProps['onClick'] = ({ key }) => {
    if(Object.values(MsgContentTypeEnum).includes(key as MsgContentTypeEnum)) {
      // 处理图表类型切换 - 检查是否为可用的图表类型
      const selectedChartType = allChartTypes.find(type => type.key === key);
      if (selectedChartType && selectedChartType.isAvailable) {
        setForceChartType(key as MsgContentTypeEnum);
      }
    }
  }

  const handleSqlCancel = () => {
    setSqlModalVisible(false);
  }
  const handleDataParse2 = async () => {
    if(parseData || data?.textSummary) return;
    setDataParseLoading(true)
    let params = {
      queryText: msg,
      agentId: agentId,
      chatId: conversationId,
      queryId: parseInfo?.queryId,
      parseId: parseInfo?.id,
    }
    const res = await getdataParse(params)
    console.log('res---', res.data.data.answer);
    setParseData(res.data.data.answer)
    setDataParseLoading(false)
  }
  const handleDataParse = () => {
    console.log('currentAgent---', currentAgent)
    if(dataParseSAnswer.length > 0 || data?.textSummary) return;

    const controller = new AbortController();
    let params = {
      queryText: msg,
      agentId: agentId,
      chatId: conversationId,
      queryId: parseInfo?.queryId,
      parseId: parseInfo?.id,
    }
    dataParseSAnswerRef.current = ''
    setDataParseLoading(true); 
    dataParseStream2(
      params,
      controller.signal,
      (chunk) => {
        if (chunk.answer) {  
          setDataParseLoading(false); 
          setdataParseSAnswer(prev => {
            const newValue = prev + chunk.answer;
            dataParseSAnswerRef.current = newValue
            if (onUpdateMessageScroll) {
              // 使用 requestAnimationFrame 确保在 DOM 更新后执行
              requestAnimationFrame(() => {
                onUpdateMessageScroll();
              });
            }
            return newValue;
          });  
        } 
      },
      (error) => {
        console.log('数据解读error',error);
        setDataParseLoading(false); 
      },
      () => {
        console.log('数据解读流式输出完成');
        setDataParseLoading(false); 
      }
    )
  }
  
  useEffect(() => {
    return () => {
      if (copyTimerRef.current) clearTimeout(copyTimerRef.current);
    };
  }, []);

  return (
    <ChartItemContext.Provider value={{ register, call }}>
      <div className={prefixCls}>
        {!isMobile && 
          // <AIBotIcon />

          // <svg 
          //   viewBox="0 0 1024 1024" 
          //   version="1.1" 
          //   xmlns="http://www.w3.org/2000/svg" 
          //   width="26" 
          //   height="26"
          //   style={{marginRight: '10px',fill: '#0057FF'}}
          // >
          //   <path 
          //     d="M802.367499 7.231995c26.751983 17.087989 23.231985 48.12797-10.495994 93.119942l-59.263963 85.631947a449.791719 449.791719 0 0 1 189.887881 210.239868A63.99996 63.99996 0 0 1 1023.99936 447.99972v383.93576a63.99996 63.99996 0 0 1-127.99992 0v-25.087984A447.74372 447.74372 0 0 1 511.99968 1023.99936a447.74372 447.74372 0 0 1-383.99976-217.087864V831.99948a63.99996 63.99996 0 0 1-127.99992 0V447.99972a63.99996 63.99996 0 0 1 101.567937-51.839968 449.471719 449.471719 0 0 1 181.375886-205.247871l-62.719961-90.559944C186.495883 55.359965 182.975886 24.319985 209.727869 7.231995c26.751983-17.087989 54.335966-3.839998 82.687948 39.935976l74.495954 104.831934A447.42372 447.42372 0 0 1 511.99968 127.99992c47.23197 0 92.735942 7.295995 135.487915 20.863987l72.191955-101.759936c28.351982-43.711973 55.935965-56.959964 82.687949-39.871976zM511.99968 191.99988a383.99976 383.99976 0 0 0-383.99976 381.119762v5.759996l0.256 11.519993A383.99976 383.99976 0 1 0 511.99968 191.99988z m-39.679975 163.711898L640.959599 767.99952h-62.143961l-48.12797-124.863922H358.399776L313.087804 767.99952h-57.919963L413.439742 355.711778h58.751963z m276.159827 0V767.99952h-54.591966V355.711778h54.591966zM441.599724 398.975751a525.567672 525.567672 0 0 1-22.207986 78.71995l-45.247972 120.959925H513.919679L470.847706 484.479697A1128.383295 1128.383295 0 0 1 441.599724 398.975751z" 
          //   />
          // </svg>
        <img
          src={avatar} 
          width={40}
          height={40}
          style={{
            marginRight: '10px',
            fontWeight: 'blod',
            display: 'inline-block',
            objectFit: 'contain', 
          }}
          alt="logo-image"
        />
        }
        
        {/* {!isMobile && <IconFont type="icon-zhinengsuanfa" className={`${prefixCls}-avatar`} />} */}
        <div className={isMobile ? `${prefixCls}-mobile-msg-card` : `${prefixCls}-w_full`}>
          <div className={`${prefixCls}-time`}>
            {parseTimeCost?.parseStartTime
              ? dayjs(parseTimeCost.parseStartTime).format('M月D日 HH:mm')
              : ''}
          </div>
           {isStreaming && !streamingAnswer ? (
            <div className={contentClass}>
              <div className={`${prefixCls}-streaming-placeholder`}>
                思考中<Loading/>
              </div>
            </div>
          ) : streamingAnswer ? (
            <div className={contentClass}>
              <MarkDown markdown={streamingAnswer} fontSize={16} />
            </div>
          ) : parseLoading || (!msgData && !difyParmes && !difyParamsFromPageQuery && !parseTip && !executeMode) ? (
            <div className={contentClass}>
              <div className={`${prefixCls}-streaming-placeholder`}>
                思考中<Loading/>
              </div>
            </div>
          ) :
          // 判断是否为问数组件：有data_query_type的新数据，老数据，或者意图分类失败
            (() => {
              const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
              const hasDataQueryType = currentDifyParmes?.data_query_type;
              const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';
              const hasIntentRecognitionError = !!intentRecognitionError;
              const isAttributionAnalysis = currentDifyParmes?.data_query_type === "归因分析";

              const shouldShowDataQuery = (msgData?.queryMode !== 'PLAIN_TEXT' && hasDataQueryType) ||
                                         isOldDataWithoutDifyParmes ||
                                         hasIntentRecognitionError ||
                                         isAttributionAnalysis; // 归因分析类型总是显示

              return shouldShowDataQuery;
            })() ? (
              <div className={contentClass}>
                <>
                  {currentAgent?.enableFeedback === 1 && !questionId && showExpandParseTip && (
                    <div style={{ marginBottom: 10 }}>
                      <ExpandParseTip
                        isSimpleMode={isSimpleMode}
                        parseInfoOptions={preParseInfoOptions}
                        agentId={agentId}
                        integrateSystem={integrateSystem}
                        parseTimeCost={parseTimeCost?.parseTime}
                        isDeveloper={isDeveloper}
                        onSelectParseInfo={onExpandSelectParseInfo}
                        onSwitchEntity={onSwitchEntity}
                        onFiltersChange={onFiltersChange}
                        onDateInfoChange={onDateInfoChange}
                        onRefresh={onRefresh}
                        handlePresetClick={handlePresetClick}
                      />
                    </div>
                  )}

                  {/* 意图分类组件 - 根据思维链步骤显示 */}
                  {(() => {
                    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
                    const hasDataQueryType = currentDifyParmes?.data_query_type;
                    const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';
                    const hasIntentRecognitionError = !!intentRecognitionError;

                    // 如果启用思维链且步骤小于0，不显示意图分类组件
                    if (enableThinkingChain && thinkingChainStep < 0) {
                      return null;
                    }

                    // 确定显示的意图类型和失败状态
                    let displayIntentType: string;
                    let isFailed = false;
                    let errorMsg = '';

                    if (hasIntentRecognitionError) {
                      // 接口报错时，显示意图分类失败
                      displayIntentType = '没有识别到用户意图';
                      isFailed = true;
                      errorMsg = intentRecognitionError;
                    } else if (hasDataQueryType) {
                      displayIntentType = currentDifyParmes.data_query_type || '数据查询';
                    } else if (intentType) {
                      displayIntentType = intentType;
                    } else if (isOldDataWithoutDifyParmes) {
                      displayIntentType = '没有识别到用户意图';
                      isFailed = true;
                    } else {
                      displayIntentType = '数据查询';
                    }

                    return (
                      <div style={{ marginBottom: 10 }}>
                        <IntentTip
                          data_query_type={displayIntentType}
                          parseInfo={parseInfo}
                          userQuestion={msg}
                          onComplete={() => {
                            setIntentRecognitionComplete(true);
                            // 触发滚动更新
                            if (onUpdateMessageScroll) {
                              requestAnimationFrame(() => {
                                onUpdateMessageScroll();
                              });
                            }
                            // 如果不是失败状态，启动思维链渲染
                            if (!isFailed) {
                              startThinkingChain();
                            }
                            // 处理意图分类完成后的逻辑（包括归因分析的延迟显示）
                            handleIntentComplete();
                          }}
                          failed={isFailed}
                          errorMsg={errorMsg}
                          enableThinkingChain={enableThinkingChain && !isFailed} // 只有启用思维链且非失败状态才显示动画
                        />
                      </div>
                    );
                  })()}

                  {/* 归因分析组件 - 当data_query_type === "归因分析"时显示 */}
                  {(() => {
                    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
                    const isAttributionAnalysis = currentDifyParmes?.data_query_type === "归因分析";

                    // 保留原先的显示条件，并添加时序控制
                    if (!isAttributionAnalysis) {
                      return null;
                    }

                    // 对于新对话和页面刷新，都需要等待意图分类完成后再显示
                    // 只有当showAttributionAnalysis为true时才显示
                    if (!showAttributionAnalysis) {
                      return null;
                    }

                    // 页面刷新时，从msgData中获取静态数据
                    const staticData = msgData?.textResult;

                    return (
                      <div style={{ marginBottom: 10 }}>
                        <AttributionAnalysisTip
                          loading={attributionAnalysisLoading}
                          streamingData={attributionAnalysisData}
                          staticData={staticData}
                          failed={attributionAnalysisFailed}
                          errorMsg={attributionAnalysisError}
                          isDeveloper={isDeveloper}
                        />
                      </div>
                    );
                  })()}

                  {(() => {
                    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
                    const hasDataQueryType = currentDifyParmes?.data_query_type;
                    const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';
                    const hasIntentRecognitionError = !!intentRecognitionError;
                    const isAttributionAnalysis = currentDifyParmes?.data_query_type === "归因分析";

                    // 如果是意图分类失败、老数据没有difyParmes，或者是归因分析，不显示ParseTip组件
                    if (hasIntentRecognitionError || isOldDataWithoutDifyParmes || isAttributionAnalysis) {
                      return null;
                    }

                    // 使用新的组件步骤计算逻辑
                    const shouldShowParseTip = !preParseMode &&
                                              (!(hasDataQueryType || intentType) || intentRecognitionComplete) &&
                                              getComponentStep('parse');

                    return shouldShowParseTip && (
                      <ParseTip
                        isSimpleMode={isSimpleMode}
                        parseLoading={parseLoading}
                        parseInfoOptions={parseInfoOptions}
                        parseTip={parseTip}
                        currentParseInfo={parseInfo}
                        agentId={agentId}
                        dimensionFilters={dimensionFilters}
                        dateInfo={dateInfo}
                        entityInfo={entityInfo}
                        integrateSystem={integrateSystem}
                        parseTimeCost={parseTimeCost?.parseTime}
                        isDeveloper={isDeveloper}
                        onSelectParseInfo={onSelectParseInfo}
                        onSwitchEntity={onSwitchEntity}
                        onFiltersChange={onFiltersChange}
                        onDateInfoChange={onDateInfoChange}
                        onRefresh={() => {
                          onRefresh();
                        }}
                        handlePresetClick={handlePresetClick}
                      />
                    );
                  })()}

                  {/* 生成SQL组件*/}
                  {(() => {
                    const hasIntentRecognitionError = !!intentRecognitionError;
                    const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
                    const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';
                    const isAttributionAnalysis = currentDifyParmes?.data_query_type === "归因分析";

                    // 如果是意图分类失败、老数据没有difyParmes，或者是归因分析，不显示生成SQL组件
                    if (hasIntentRecognitionError || isOldDataWithoutDifyParmes || isAttributionAnalysis) {
                      return null;
                    }

                    return getComponentStep('sql') && parseInfo?.sqlInfo && (
                      <GenerateSqlTip
                        sqlInfo={parseInfo.sqlInfo}
                        sqlTimeCost={parseTimeCost?.sqlTime}
                        executeErrorMsg={executeErrorMsg}
                        isDeveloper={isDeveloper}
                      />
                    );
                  })()}
                </>

                {(() => {
                  const hasIntentRecognitionError = !!intentRecognitionError;
                  const currentDifyParmes = difyParmes || difyParamsFromPageQuery;
                  const isOldDataWithoutDifyParmes = msgData && !currentDifyParmes && msgData.queryMode !== 'PLAIN_TEXT';
                  const isAttributionAnalysis = currentDifyParmes?.data_query_type === "归因分析";

                  // 如果是意图分类失败、老数据没有difyParmes，或者是归因分析，不显示executeMode相关组件
                  if (hasIntentRecognitionError || isOldDataWithoutDifyParmes || isAttributionAnalysis) {
                    return null;
                  }

                  return getComponentStep('execute');
                })() && (
                  <Spin spinning={entitySwitchLoading}>
                    <div style={{ minHeight: 50 }}>
                      {/* {!isMobile &&
                        parseInfo?.sqlInfo &&
                        isDeveloper &&
                        isDebugMode &&
                        !isSimpleMode && (
                          <SqlItem
                            agentId={agentId}
                            queryId={parseInfo.queryId}
                            question={msg}
                            llmReq={llmReq}
                            llmResp={llmResp}
                            integrateSystem={integrateSystem}
                            queryMode={parseInfo.queryMode}
                            sqlInfo={parseInfo.sqlInfo}
                            sqlTimeCost={parseTimeCost?.sqlTime}
                            executeErrorMsg={executeErrorMsg}
                          />
                        )}    */}
                      
                        <div className={`${prefixCls}-content-right-tools`}>
                          <div>
                            {
                              currentAgent?.chatAppConfig?.DATA_INTERPRETER?.enable &&(
                                <Tooltip title="数据解读" placement="top" mouseEnterDelay={0.1}> 
                                  <Button   
                                    type="text"
                                    onClick={handleDataParse}   
                                    style={{ 
                                      width: '32px',
                                      height: '32px',
                                      padding: 0 
                                    }}
                                  >  
                                    <ReadOutlined/>
                                  </Button>
                                </Tooltip>
                              )
                            }
                            {allChartTypes.length > 0 && (
                              <Dropdown
                                menu={{
                                  items: chartSwitchMenuItems,
                                  onClick: chartSwitchMenuClick,
                                }}
                                trigger={['click']}
                                placement="bottomLeft"
                                disabled={!allChartTypes.some(type => type.isAvailable)}
                              >
                                <Tooltip
                                  title={!allChartTypes.some(type => type.isAvailable) ? '当前数据不支持图表切换' : '切换图表'}
                                  placement="top"
                                  mouseEnterDelay={0.1}
                                >
                                  <Button
                                    type="text"
                                    style={{
                                      width: '32px',
                                      height: '32px',
                                      padding: 0,
                                      color: !allChartTypes.some(type => type.isAvailable) ? '#d9d9d9' : 'inherit',
                                      cursor: !allChartTypes.some(type => type.isAvailable) ? 'not-allowed' : 'pointer'
                                    }}
                                    disabled={!allChartTypes.some(type => type.isAvailable)}
                                  >
                                    {currentChartType === MsgContentTypeEnum.METRIC_TREND ? <LineChartOutlined/> :
                                     currentChartType === MsgContentTypeEnum.METRIC_PIE ? <PieChartOutlined/> :
                                     <BarChartOutlined/>}
                                  </Button>
                                </Tooltip>
                              </Dropdown>
                            )}
                            <Dropdown 
                              menu={{  
                                items: moreItems,  
                                onClick: moretMenuClick,  
                              }}  
                              trigger={['click']}  
                              placement="bottomLeft"  
                            >
                              <Button   
                                type="text"   
                                style={{ 
                                  width: '32px',
                                  height: '32px',
                                  padding: 0 
                                }}
                              >  
                                <MoreOutlined/>
                                {/* <DownOutlined style={{ marginLeft: '8px', fontSize: '12px' }} />   */}
                              </Button>  
                            </Dropdown>
                          </div>
                        </div>         
                        <ExecuteItem
                          isSimpleMode={isSimpleMode}
                          queryId={parseInfo?.queryId}
                          question={msg}
                          queryMode={parseInfo?.queryMode}
                          executeLoading={executeLoading}
                          executeTip={executeTip}
                          executeErrorMsg={executeErrorMsg}
                          chartIndex={0}
                          data={data}
                          triggerResize={triggerResize}
                          executeItemNode={executeItemNode}
                          isDeveloper={isDeveloper}
                          renderCustomExecuteNode={renderCustomExecuteNode}
                          forceChartType={forceChartType}
                        />
                        {dataParseLoading && !data?.textSummary &&(
                          <div className={`${prefixCls}-title-bar`}>
                            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
                            <div className={`${prefixCls}-step-title`}>
                              数据解读中<Loading />
                            </div>
                          </div>
                        )}

                        {data?.textSummary && (
                          <div>
                            <div className={`${prefixCls}-title-bar`}>
                              <CheckCircleFilled className={`${prefixCls}-step-icon`} />
                              <div className={`${prefixCls}-step-title`}  style={{height: 38}}>
                                数据解读
                              </div>
                            </div>
                            <div className={`${prefixCls}-data-parser-container`}>
                              <MarkDown markdown={data?.textSummary} />
                            </div>
                          </div>
                        )}
                        {/* 块级渲染 */}
                        {
                          parseData &&(
                            <div>
                              <div className={`${prefixCls}-title-bar`}>
                                <CheckCircleFilled className={`${prefixCls}-step-icon`} />
                                <div className={`${prefixCls}-step-title`} style={{height: 38}}>
                                  数据解读
                                </div>
                              </div>
                              <div className={`${prefixCls}-data-parser-container`}>
                                <MarkDown markdown={parseData} />
                              </div>
                            </div>
                          )
                        }
                        {/* 流式渲染 */}
                        {dataParseSAnswer && !data?.textSummary &&(
                          <div>
                            <div className={`${prefixCls}-title-bar`}>
                              <CheckCircleFilled className={`${prefixCls}-step-icon`} />
                              <div className={`${prefixCls}-step-title`} style={{height: 38}}>
                                数据解读
                              </div>
                            </div>
                            <div className={`${prefixCls}-data-parser-container`}>
                              <MarkDown markdown={dataParseSAnswer} />
                            </div>
                          </div>
                        )}
                    </div>
                  </Spin>
                )}
              </div>
            ): (
              <div  className={contentClass}>
                <MarkDown markdown={msgData?.textResult || ''} fontSize={16} />
              </div>
            )
          }
          
          { !isMobile &&
            parseInfo?.sqlInfo &&
            isDeveloper &&
            isDebugMode &&
            !isSimpleMode && (
              <SqlViewModel 
                onCancel={handleSqlCancel}
                sqlModalVisible={sqlModalVisible}
                agentId={agentId}
                queryId={parseInfo.queryId}
                question={msg}
                llmReq={llmReq}
                llmResp={llmResp}
                integrateSystem={integrateSystem}
                queryMode={parseInfo.queryMode}
                sqlInfo={parseInfo.sqlInfo}
                sqlTimeCost={parseTimeCost?.sqlTime}
                executeErrorMsg={executeErrorMsg}
              />
            )
          }
          
          {(parseTip !== '' || (executeMode && !executeLoading)) &&
            parseInfo?.queryMode !== 'PLAIN_TEXT' && (
              <Tools
                isLastMessage={isLastMessage}
                queryId={parseInfo?.queryId || 0}
                scoreValue={score}
                isParserError={isParserError}
                onExportData={() => {
                  onExportData();
                }}
                isSimpleMode={isSimpleMode}
                onReExecute={queryId => {
                  deleteQueryInfo(queryId);
                }}
                copied={copied}
                onCopy={handleCopy}
              />
            )}
        </div>
      </div>
    </ChartItemContext.Provider>
  );
};

export default ChatItem;
