<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suite.chatdatabi.chat.server.persistence.mapper.custom.ShowCaseCustomMapper">
    <resultMap id="BaseResultMap" type="com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO">
        <id column="question_id" jdbcType="BIGINT" property="questionId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="query_state" jdbcType="INTEGER" property="queryState" />
        <result column="chat_id" jdbcType="BIGINT" property="chatId" />
        <result column="score" jdbcType="INTEGER" property="score" />
        <result column="feedback" jdbcType="VARCHAR" property="feedback" />
        <result column="dify_parmes" jdbcType="VARCHAR" property="difyParmes" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO">
        <result column="query_text" jdbcType="LONGVARCHAR" property="queryText" />
        <result column="query_result" jdbcType="LONGVARCHAR" property="queryResult" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and
                                    #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem"
                                             open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, model_id, name, biz_name, description, type, created_at, created_by, updated_at, updated_by
    </sql>
    <sql id="Blob_Column_List">
        typeParams
    </sql>

    <select id="queryShowCase" resultMap="ResultMapWithBLOBs">
        select q1.*
        from s2_chat_query q1
        join (
            select distinct chat_id
            from (
               select *
               from s2_chat_query
               where query_state = 1 and agent_id = ${agentId} and score = 5
               <if test="userName != null and userName != ''">
                     and user_name = #{userName}
               </if>
               order by chat_id desc
            ) a
            limit #{start}, #{limit}
        ) q2 on q1.chat_id = q2.chat_id
        where agent_id = ${agentId} and score = 5
    </select>
    <select id="getChatQuery" resultType="com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO" >
        select *
        from s2_chat_query q1
        where question_id = ${questionId}
    </select>
</mapper>
