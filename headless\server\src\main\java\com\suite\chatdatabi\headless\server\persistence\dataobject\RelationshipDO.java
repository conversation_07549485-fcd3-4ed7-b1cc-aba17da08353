package com.suite.chatdatabi.headless.server.persistence.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("s2_relationship_config")
public class RelationshipDO {


    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer pid;
    private String filed;
    private String type;
    private String relationName;
    private Integer datasetId;


}
