package com.suite.chatdatabi.chat.server.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.suite.chatdatabi.chat.api.pojo.request.ChatExecuteReq;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.chat.api.pojo.response.ChatParseResp;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.agent.Agent;
import com.suite.chatdatabi.chat.server.executor.ChatQueryExecutor;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.chat.server.persistence.mapper.ChatQueryDOMapper;
import com.suite.chatdatabi.chat.server.pojo.ExecuteContext;
import com.suite.chatdatabi.chat.server.service.AgentService;
import com.suite.chatdatabi.chat.server.service.ChatQueryService;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.common.util.ContextUtils;
import com.suite.chatdatabi.headless.api.pojo.SchemaElementMatch;
import com.suite.chatdatabi.headless.api.pojo.SchemaMapInfo;
import com.suite.chatdatabi.headless.api.pojo.SemanticParseInfo;
import com.suite.chatdatabi.headless.api.pojo.SemanticSchema;
import com.suite.chatdatabi.headless.api.pojo.enums.MapModeEnum;
import com.suite.chatdatabi.headless.api.pojo.request.QueryNLReq;
import com.suite.chatdatabi.headless.api.pojo.request.QuerySqlReq;
import com.suite.chatdatabi.headless.api.pojo.request.SemanticQueryReq;
import com.suite.chatdatabi.headless.api.pojo.response.ParseResp;
import com.suite.chatdatabi.headless.api.pojo.response.S2Term;
import com.suite.chatdatabi.headless.api.pojo.response.SemanticTranslateResp;
import com.suite.chatdatabi.headless.chat.ChatQueryContext;
import com.suite.chatdatabi.headless.chat.knowledge.helper.HanlpHelper;
import com.suite.chatdatabi.headless.chat.mapper.EmbeddingMapper;
import com.suite.chatdatabi.headless.chat.mapper.KeywordMapper;
import com.suite.chatdatabi.headless.chat.mapper.SchemaMapper;
import com.suite.chatdatabi.headless.server.facade.service.ChatLayerService;
import com.suite.chatdatabi.headless.server.facade.service.SemanticLayerService;
import com.suite.chatdatabi.headless.server.persistence.dataobject.RelationshipDO;
import com.suite.chatdatabi.headless.server.service.DataSetService;
import com.suite.chatdatabi.headless.server.service.RelationshipService;
import com.suite.chatdatabi.headless.server.service.SchemaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.support.SpringFactoriesLoader;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GraphQueryServiceImpl implements GraphQueryService {

    @Autowired
    AgentService agentService;

    @Autowired
    DataSetService dataSetService;

    @Autowired
    RelationshipService relationshipService;

    @Autowired
    SchemaService schemaService;

    @Autowired
    SemanticLayerService semanticLayerService;

    @Autowired
    private ChatQueryDOMapper chatQueryDOMapper;

    @Autowired ChatQueryService chatQueryService;


    @Override
    public Set<String> segmentText(String queryText, Integer agentId) {
        // 测试数据
//        ChatParseReq chatParseReq = new ChatParseReq();
//        chatParseReq.setQueryText(queryText);
//        chatParseReq.setAgentId(agentId);
//
//        // 获取 Agent 关联的数据集
//        Agent agent = agentService.getAgent(agentId);
//        Set<Long> dataSetIds = agent.getDataSetIds();
//
//        // 获取 modelIdToDataSetIds 映射
//        Map<Long, List<Long>> modelIdToDataSetIds =
//                dataSetService.getModelIdToDataSetIds(new ArrayList<>(dataSetIds), User.getDefaultUser());
//
//        // 获取指标分词
//        List<S2Term> terms = HanlpHelper.getTerms(queryText, modelIdToDataSetIds);
//        return terms.stream().filter(item -> item.getNature().toString().endsWith("_metric")).map(S2Term::getWord).collect(Collectors.toSet());
        return segmentText2(queryText,agentId);
    }

    public Set<String> segmentText2(String queryText,Integer agentId){

        Agent agent = agentService.getAgent(agentId);
        Set<Long> dataSetIds = agent.getDataSetIds();
        // 1. 构建查询上下文，使用系统默认配置
        ChatQueryContext queryContext = buildQueryContext(queryText, dataSetIds);

        // 2. 按照系统设置执行匹配策略
        executeSystemMatching(queryContext);

        // 3. 过滤出完全匹配的结果
        List<SchemaElementMatch> results = processFullMatchResults(queryContext);

        log.info("多层次匹配完成，返回完全匹配结果: {}", results);
        if (CollectionUtils.isEmpty(results)){
            return new HashSet<>();
        }
        return results.stream().map(SchemaElementMatch::getWord).collect(Collectors.toSet());
    }

    private ChatQueryContext buildQueryContext(String queryText, Set<Long> dataSetIds) {
        QueryNLReq request = new QueryNLReq();
        request.setQueryText(queryText);
        request.setDataSetIds(dataSetIds);
        // 使用系统默认的匹配模式
        request.setMapModeEnum(MapModeEnum.LOOSE);

        ChatQueryContext queryContext = new ChatQueryContext(request);

        // 设置语义模式和数据集映射
        queryContext.setSemanticSchema(schemaService.getSemanticSchema(dataSetIds));
        queryContext.setModelIdToDataSetIds(dataSetService.getModelIdToDataSetIds(
                new ArrayList<>(dataSetIds), null));

        return queryContext;
    }

    private void executeSystemMatching(ChatQueryContext queryContext) {
        // 按照系统设置的顺序执行匹配器，完全遵循系统配置
        List<SchemaMapper> systemMappers = getSystemMappers();

        for (SchemaMapper mapper : systemMappers) {
            try {
                // 每个mapper都会检查自己的accept条件和系统配置
                mapper.map(queryContext);
                log.debug("执行系统匹配器: {}", mapper.getClass().getSimpleName());
            } catch (Exception e) {
                log.error("系统匹配器执行失败: {}", mapper.getClass().getSimpleName(), e);
            }
        }
    }

    private List<SchemaMapper> getSystemMappers() {
        // 直接通过 SPI 加载所有 SchemaMapper
        List<SchemaMapper> allMappers = SpringFactoriesLoader.loadFactories(
                SchemaMapper.class,
                Thread.currentThread().getContextClassLoader()
        );

        List<SchemaMapper> mappers = new ArrayList<>();

        for (SchemaMapper mapper : allMappers) {
            if (mapper instanceof KeywordMapper) {
                mappers.add(mapper);
            }
            if (mapper instanceof EmbeddingMapper) {
                mappers.add(mapper);
            }
        }

        return mappers;
    }

    private List<SchemaElementMatch> processFullMatchResults(ChatQueryContext queryContext) {

        SchemaMapInfo mapInfo = queryContext.getMapInfo();
        if (mapInfo == null || CollectionUtils.isEmpty(mapInfo.getDataSetElementMatches())) {
            return Collections.emptyList();
        }

        // 1. 收集所有匹配结果
        List<SchemaElementMatch> allMatches = mapInfo.getDataSetElementMatches().values()
                .stream()
                .flatMap(List::stream)
                .toList();

        // 2. 只保留完全匹配的结果 (similarity = 1.0)
        List<SchemaElementMatch> fullMatches = allMatches.stream()
                .filter(SchemaElementMatch::isFullMatched)
                .toList();

        // 3. 按照系统的排序规则排序
//        fullMatches.sort((m1, m2) -> {
//            // 检测词长度长的优先（更精确的匹配）
//            int lengthCompare = Integer.compare(m2.getDetectWord().length(), m1.getDetectWord().length());
//            if (lengthCompare != 0) {
//                return lengthCompare;
//            }
//
//            // 使用频率排序
//            return Long.compare(m2.getFrequency(), m1.getFrequency());
//        });

        // 4. 限制返回结果数量
        return fullMatches;
    }
    @Override
    public List<RelationshipDO> queryRelationshipDataByKeyWord(List<String> segmentResult, Integer agentId) {
        Agent agent = agentService.getAgent(agentId);
        Set<Long> dataSetIds = agent.getDataSetIds();
        List<RelationshipDO> list  = relationshipService.queryAllDataSetList(dataSetIds);
        // 找到当前用户分词的节点
        List<RelationshipDO> relationshipDOList = list.stream().filter(item -> segmentResult.contains(item.getRelationName())).collect(Collectors.toList());
        if(relationshipDOList.isEmpty()){
            return new ArrayList<>();
        }

        List<RelationshipDO> allChildNodes = new ArrayList<>();
        for (RelationshipDO node : relationshipDOList) {
            getAllChildNodes(list, node, allChildNodes);
        }
        return allChildNodes.stream().distinct().collect(Collectors.toList());
    }

    private void getAllChildNodes(List<RelationshipDO> allNodes, RelationshipDO parentNode, List<RelationshipDO> childNodes) {
        List<RelationshipDO> directChildren = allNodes.stream()
                .filter(node -> node.getPid() != null && node.getPid().equals(parentNode.getId()))
                .toList();

        childNodes.addAll(directChildren);

        for (RelationshipDO child : directChildren) {
            getAllChildNodes(allNodes, child, childNodes);
        }
    }

    @Override
    public SemanticSchema getDataSetSchema(Integer agentId) {
        Agent agent = agentService.getAgent(agentId);
        if (agent != null && agent.getDataSetIds() != null && !agent.getDataSetIds().isEmpty()) {
           return schemaService.getSemanticSchema(agent.getDataSetIds());
        }
        return null;
    }

    @Override
    public String getNl2Data(String queryText, Integer agentId, User user) {

        ChatParseReq chatParseReq = new ChatParseReq();
        chatParseReq.setQueryText(queryText);
        chatParseReq.setAgentId(agentId);
        chatParseReq.setUser( user);

        String queryResult = chatQueryService.nl2Data(chatParseReq);
        log.info("queryResult: {}", queryResult);
        // 获取语义SQL
//        QueryNLReq queryNLReq = new QueryNLReq();
//        queryNLReq.setQueryText(chatParseReq.getQueryText());
//        queryNLReq.setUser(chatParseReq.getUser());
//        ParseResp parseResp = chatLayerService.parse(queryNLReq);
//        // 转换最终SQL
//        SemanticParseInfo parseInfo = parseResp.getSelectedParses().get(0);
//        QuerySqlReq sqlReq = new QuerySqlReq();
//        sqlReq.setSql(parseInfo.getSqlInfo().getCorrectedS2SQL());
//        sqlReq.setSqlInfo(parseInfo.getSqlInfo());
//
//        SemanticTranslateResp translateResp = null;
//        try {
//            translateResp = semanticLayerService.translate(
//                    buildSemanticQueryReq(parseInfo), User.getDefaultUser());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        String querySQL = translateResp.getQuerySQL();
//         log.info("querySQL: {}", querySQL);
//        ChatExecuteReq executeReq = new ChatExecuteReq();
//        executeReq.setParseId(parseResp.getSelectedParses().get(0).getId());
//        executeReq.setQueryText(chatParseReq.getQueryText());
//        executeReq.setChatId(chatParseReq.getChatId());
//        executeReq.setUser(User.getDefaultUser());
//        executeReq.setAgentId(chatParseReq.getAgentId());
//        executeReq.setSaveAnswer(true);
//
//        QueryResult queryResult = new QueryResult();
//        ExecuteContext executeContext = buildExecuteContext(executeReq);
//
//        for (ChatQueryExecutor chatQueryExecutor : chatQueryExecutors) {
//            if (chatQueryExecutor.accept(executeContext)) {
//                queryResult = chatQueryExecutor.execute(executeContext);
//                if (queryResult != null) {
//                    break;
//                }
//            }
//        }
//
//        return queryResult;
        return queryResult;
    }

    @Override
    public Integer saveQueryResult(ChatQueryDO chatQueryDO) {
        return chatQueryDOMapper.insert(chatQueryDO);
    }

    private SemanticQueryReq buildSemanticQueryReq(SemanticParseInfo parseInfo) {
        QuerySqlReq querySqlReq = new QuerySqlReq();
        querySqlReq.setDataSetId(parseInfo.getDataSetId());
        querySqlReq.setDataSetName(parseInfo.getDataSet().getName());
        querySqlReq.setSql(parseInfo.getSqlInfo().getCorrectedS2SQL());
        querySqlReq.setSqlInfo(parseInfo.getSqlInfo());
        return querySqlReq;
    }
}
