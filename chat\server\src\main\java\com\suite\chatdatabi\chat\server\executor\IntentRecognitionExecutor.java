package com.suite.chatdatabi.chat.server.executor;

import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.agent.Agent;
import com.suite.chatdatabi.chat.server.pojo.ExecuteContext;
import com.suite.chatdatabi.chat.server.service.AgentService;
import com.suite.chatdatabi.common.pojo.ChatApp;
import com.suite.chatdatabi.common.pojo.enums.AppModule;
import com.suite.chatdatabi.common.util.ChatAppManager;
import com.suite.chatdatabi.common.util.ContextUtils;
import com.suite.chatdatabi.headless.api.pojo.response.QueryState;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.provider.ModelProvider;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class IntentRecognitionExecutor implements ChatQueryExecutor{
    public static final String APP_KEY = "INTENT_RECOGNITION";

    private static final String INTENT_RECOGNITION_INSTRUCTION =
            "#Role: 你是一个严格的意图识别专家\n" +
                    "#Task: 判断用户输入的自然语言严格属于以下哪种类型\n" +
                    "#Rules:\n" +
                    "1. **数据查询**：仅请求获取具体数据/信息（如数字、统计、记录、资料查询），且不涉及原因分析或报告生成。\n" +
                    "   - 特征：询问『是什么』『多少』『有哪些』，不包含『为什么』『如何导致』『分析原因』等。\n" +
                    "   - 示例：『上个月销售额是多少？』『用户年龄分布数据』\n" +
                    "2. **归因分析**：要求分析现象的原因或影响因素，分为两类：\n" +
                    "   - **数据归因**：量化分解指标变化/构成（如『销售额增长30%的原因是什么？各渠道贡献多少？』）\n" +
                    "   - **问题归因**：诊断负面事件的根因（如『服务器宕机的根本原因是什么？』）\n" +
                    "3. **生成报告**：明确要求生成结构化分析报告、总结或汇报材料（如『生成一份季度销售分析报告』）\n" +
                    "4. **DIFY**：不符合以上三类的所有情况（操作指导、知识问答、系统指令、闲聊等）\n" +
                    "#Output Rules:\n" +
                    "- 必须四选一：'数据查询'、'归因分析'、'生成报告'、'DIFY'\n" +
                    "- 禁止解释，仅输出结果\n" +
                    "#Examples:\n" +
                    "输入：『本周DAU是多少？』 → 数据查询\n" +
                    "输入：『转化率下降的原因是什么？』 → 归因分析\n" +
                    "输入：『写一份用户流失分析报告』 → 生成报告\n" +
                    "输入：『如何重置密码？』 → DIFY\n" +
                    "#Question: {{question}}\n" +
                    "#Intent:";
    public IntentRecognitionExecutor() {
        ChatAppManager.register(APP_KEY, ChatApp.builder()
                .prompt(INTENT_RECOGNITION_INSTRUCTION)
                .name("意图分类")
                .appModule(AppModule.CHAT)
                .description("识别用户输入的意图类型，判断是否为数据查询")
                .enable(false)  // 默认启用
                .build());
    }
    @Override
    public boolean accept(ExecuteContext executeContext) {
        return "INTENT_RECOGNITION".equals(executeContext.getParseInfo().getQueryMode());
    }

    @Override
    public QueryResult execute(ExecuteContext executeContext) {
        AgentService agentService = ContextUtils.getBean(AgentService.class);
        Agent chatAgent = agentService.getAgent(executeContext.getAgent().getId());
        ChatApp chatApp = chatAgent.getChatAppConfig().get(APP_KEY);
        if (Objects.isNull(chatApp) || !chatApp.isEnable()) {
            return null;
        }

        // Create variables map for template substitution
        Map<String, Object> variables = new HashMap<>();
        variables.put("question", executeContext.getRequest().getQueryText());

        Prompt prompt = PromptTemplate.from(chatApp.getPrompt()).apply(Collections.EMPTY_MAP);
        ChatLanguageModel chatLanguageModel =
                ModelProvider.getChatModel(chatApp.getChatModelConfig());
        Response<AiMessage> response = chatLanguageModel.generate(prompt.toUserMessage());

        QueryResult result = new QueryResult();
        result.setQueryState(QueryState.SUCCESS);
        result.setQueryMode(executeContext.getParseInfo().getQueryMode());
        result.setTextResult(response.content().text());

        return result;
    }

    @Override
    public String getChatHisQuery(ExecuteContext executeContext) {
        return "";
    }
}
