package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.fastjson.JSON;
import com.facebook.presto.jdbc.internal.jackson.core.JsonProcessingException;
import com.facebook.presto.jdbc.internal.jackson.databind.ObjectMapper;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.headless.api.pojo.response.QueryState;

import java.util.HashMap;
import java.util.Map;

public class SaveReportNode implements NodeAction {
    private GraphQueryService graphQueryService;

    public SaveReportNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        Object reportResult =  state.value("reportResult").orElse( null);
        Object agentId = state.value("agentId").orElse( null);
        Object chatId = state.value("chatId").orElse(null);
        Object queryText =  state.value("queryText").orElse( null);
        User user = (User) state.value("user").orElse(null);
        saveResult(agentId,chatId,queryText,reportResult,user);
        return Map.of();
    }

    private void saveResult(Object agentId, Object chatId, Object queryText, Object finalResult,User user) throws JsonProcessingException {
        ChatQueryDO chatQueryDO = new ChatQueryDO();
        QueryResult queryResult = new QueryResult();
        queryResult.setQueryMode("PLAIN_TEXT");
        queryResult.setQueryState(QueryState.SUCCESS);
        queryResult.setTextResult(String.valueOf(finalResult));
        chatQueryDO.setQueryText(queryText.toString());
        chatQueryDO.setQueryResult(JSON.toJSONString(queryResult));
        chatQueryDO.setAgentId(Integer.parseInt(agentId.toString()));
        chatQueryDO.setChatId(Long.parseLong(chatId.toString()));
        chatQueryDO.setUserName(user.getName());
        chatQueryDO.setQueryState(1);

        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, String> difyParmes = new HashMap<>();
        difyParmes.put("data_query_type","归因分析");
        String json = objectMapper.writeValueAsString(difyParmes);
        chatQueryDO.setDifyParmes(json);
        graphQueryService.saveQueryResult(chatQueryDO);
    }
}
