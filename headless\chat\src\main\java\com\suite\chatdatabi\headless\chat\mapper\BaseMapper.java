package com.suite.chatdatabi.headless.chat.mapper;

import com.suite.chatdatabi.headless.api.pojo.DataSetSchema;
import com.suite.chatdatabi.headless.api.pojo.SchemaElement;
import com.suite.chatdatabi.headless.api.pojo.SchemaElementMatch;
import com.suite.chatdatabi.headless.api.pojo.SchemaElementType;
import com.suite.chatdatabi.headless.api.pojo.SchemaMapInfo;
import com.suite.chatdatabi.headless.api.pojo.SemanticSchema;
import com.suite.chatdatabi.headless.api.pojo.response.S2Term;
import com.suite.chatdatabi.headless.chat.ChatQueryContext;
import com.suite.chatdatabi.headless.chat.knowledge.helper.HanlpHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
public abstract class BaseMapper implements SchemaMapper {

    @Override
    public void map(ChatQueryContext chatQueryContext) {
        if (!accept(chatQueryContext)) {
            return;
        }

        String simpleName = this.getClass().getSimpleName();
        long startTime = System.currentTimeMillis();
        log.debug("before {},mapInfo:{}", simpleName,
                chatQueryContext.getMapInfo().getDataSetElementMatches());

        try {
            doMap(chatQueryContext);
            MapFilter.filter(chatQueryContext);
        } catch (Exception e) {
            log.error("work error", e);
        }

        long cost = System.currentTimeMillis() - startTime;
        log.info("after {},cost:{},mapInfo:{}", simpleName, cost,
                chatQueryContext.getMapInfo().getDataSetElementMatches());
    }

    public abstract void doMap(ChatQueryContext chatQueryContext);

    public boolean accept(ChatQueryContext chatQueryContext) {
        return true;
    }

    public void addToSchemaMap(SchemaMapInfo schemaMap, Long dataSetId,
            SchemaElementMatch newElementMatch) {
        Map<Long, List<SchemaElementMatch>> dataSetElementMatches =
                schemaMap.getDataSetElementMatches();
        List<SchemaElementMatch> schemaElementMatches =
                dataSetElementMatches.computeIfAbsent(dataSetId, k -> new ArrayList<>());

        AtomicBoolean shouldAddNew = new AtomicBoolean(true);

        schemaElementMatches.removeIf(existingElementMatch -> {
            if (isEquals(existingElementMatch, newElementMatch)) {
                if (newElementMatch.getSimilarity() > existingElementMatch.getSimilarity()) {
                    return true;
                } else {
                    shouldAddNew.set(false);
                }
            }
            return false;
        });

        if (shouldAddNew.get()) {
            schemaElementMatches.add(newElementMatch);
        }
    }

    private static boolean isEquals(SchemaElementMatch existElementMatch,
            SchemaElementMatch newElementMatch) {
        SchemaElement existElement = existElementMatch.getElement();
        SchemaElement newElement = newElementMatch.getElement();
        if (!existElement.equals(newElement)) {
            return false;
        }
        if (SchemaElementType.VALUE.equals(newElement.getType())) {
            return existElementMatch.getWord().equalsIgnoreCase(newElementMatch.getWord());
        }
        return true;
    }

    public SchemaElement getSchemaElement(Long dataSetId, SchemaElementType elementType,
            Long elementID, SemanticSchema semanticSchema) {
        SchemaElement element = new SchemaElement();
        DataSetSchema dataSetSchema = semanticSchema.getDataSetSchemaMap().get(dataSetId);
        if (Objects.isNull(dataSetSchema)) {
            return null;
        }
        SchemaElement elementDb = dataSetSchema.getElement(elementType, elementID);
        if (Objects.isNull(elementDb)) {
            return null;
        }
        BeanUtils.copyProperties(elementDb, element);
        element.setAlias(getAlias(elementDb));
        return element;
    }

    public List<String> getAlias(SchemaElement element) {
        if (!SchemaElementType.VALUE.equals(element.getType())) {
            return element.getAlias();
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(element.getAlias())
                && StringUtils.isNotEmpty(element.getName())) {
            return element.getAlias().stream()
                    .filter(aliasItem -> aliasItem.contains(element.getName()))
                    .collect(Collectors.toList());
        }
        return element.getAlias();
    }

    public <T> List<T> getMatches(ChatQueryContext chatQueryContext, MatchStrategy matchStrategy) {
        // 获取用户查询文本
        String queryText = chatQueryContext.getRequest().getQueryText();
        // 第一次用queryText和modelIdToDataSetIds，第一次获取基础术语
        List<S2Term> terms =
                HanlpHelper.getTerms(queryText, chatQueryContext.getModelIdToDataSetIds());
        // 通过 HanLP 进行术语抽取（两次过滤）
        terms = HanlpHelper.getTerms(terms, chatQueryContext.getRequest().getDataSetIds());
        // 调用matchStrategy.match方法：传入chatQueryContext、terms和dataSetIds，得到一个Map >的匹配结果。
        Map<MatchText, List<T>> matchResult = matchStrategy.match(chatQueryContext, terms,
                chatQueryContext.getRequest().getDataSetIds());
        List<T> matches = new ArrayList<>();
        // 处理matchResult：如果结果为空，返回空列表。
        if (Objects.isNull(matchResult)) {
            return matches;
        }
        // 通过流处理找到第一个非空的条目，并将其值作为结果返回。
        Optional<List<T>> first = matchResult.entrySet().stream()
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .map(entry -> entry.getValue()).findFirst();

        if (first.isPresent()) {
            matches = first.get();
        }
        return matches;
    }
}
