package com.suite.chatdatabi.chat.server.service;

import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.headless.api.pojo.SemanticSchema;
import com.suite.chatdatabi.headless.server.persistence.dataobject.RelationshipDO;

import java.util.List;
import java.util.Set;

public interface GraphQueryService {

    /**
     * 分词
     *
     * @param queryText
     * @param agentId
     * @return
     */
    Set<String> segmentText(String queryText, Integer agentId);

    /**
     * 查询血缘关系
     *
     * @param segmentResult
     * @param agentId
     * @return
     */
    List<RelationshipDO> queryRelationshipDataByKeyWord(List<String> segmentResult, Integer agentId);

    /**
     *   获取schema信息
     * @param agentId 当前agentId
     * @return
     */
    SemanticSchema getDataSetSchema(Integer agentId);

    /**
     * 获取nl2data数据
     *
     * @param queryText nl2sql的问题
     * @param user
     * @return nl2data的结果json
     */
    String getNl2Data(String queryText, Integer agentId, User user);

    /**
     * 保存查询结果
     * @param chatQueryDO
     * @return
     */
    Integer saveQueryResult(ChatQueryDO chatQueryDO);
}
