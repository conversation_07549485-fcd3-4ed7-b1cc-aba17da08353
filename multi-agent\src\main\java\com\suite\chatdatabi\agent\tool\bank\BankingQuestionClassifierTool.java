package com.suite.chatdatabi.agent.tool.bank;

import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.tool.BaseTool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Data
public class BankingQuestionClassifierTool implements BaseTool {
    private AgentContext agentContext;

    @Override
    public String getName() {
        return "bank_question_classifier";
    }

    @Override
    public String getDescription() {
        return "银行数据分析问题分类工具，用于判断用户问题是数据查询、归因分析、数据洞察还是报告生成类型";
    }

    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> questionParam = new HashMap<>();
        questionParam.put("type", "string");
        questionParam.put("description", "用户输入的银行数据分析相关问题");
        properties.put("question", questionParam);

        parameters.put("properties", properties);
        parameters.put("required", Collections.singletonList("question"));
        return parameters;
    }

    @Override
    public Object execute(Object input) {
        try {
            Map<String, Object> params = (Map<String, Object>) input;
            String question = (String) params.get("question");

            String questionType = classifyQuestion(question);

            // 返回字符串而不是 Map
            return String.format("问题分类结果：%s，原问题：%s",
                    questionType, question);

        } catch (Exception e) {
            log.error("{} bank_question_classifier error", agentContext.getRequestId(), e);
            // 返回错误信息字符串而不是 null
            return "分类工具执行失败：" + e.getMessage();
        }
    }

    private String classifyQuestion(String question) {
        // 数据查询关键词
        String[] queryKeywords = {"查询", "获取", "显示", "列出", "统计", "多少", "什么", "哪些"};
        // 归因分析关键词
        String[] analysisKeywords = {"为什么", "下降",  "上升",  "原因", "分析", "影响", "趋势", "变化", "对比"};
        // 报告生成关键词
        String[] reportKeywords = {"报告", "总结", "汇总", "生成", "输出", "展示"};

        int queryScore = countKeywords(question, queryKeywords);
        int analysisScore = countKeywords(question, analysisKeywords);
        int reportScore = countKeywords(question, reportKeywords);

        if (reportScore > queryScore && reportScore > analysisScore) {
            agentContext.setScenario("bank_analysis");
            return "报告生成";
        } else if (analysisScore > queryScore) {
            agentContext.setScenario("bank_analysis");
            return "归因分析";
        } else {
            agentContext.setScenario("bank_analysis");
            return "数据查询";
        }
    }

    private int countKeywords(String question, String[] keywords) {
        int count = 0;
        for (String keyword : keywords) {
            if (question.contains(keyword)) {
                count++;
            }
        }
        return count;
    }

//    private double getConfidenceScore(String question, String type) {
//        // 简单的置信度计算逻辑
//        return 0.85;
//    }
}