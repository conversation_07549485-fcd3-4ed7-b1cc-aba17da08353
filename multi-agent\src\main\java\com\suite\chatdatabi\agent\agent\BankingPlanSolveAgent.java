package com.suite.chatdatabi.agent.agent;

import com.suite.chatdatabi.agent.dto.Message;
import com.suite.chatdatabi.agent.tool.BaseTool;
import com.suite.chatdatabi.agent.tool.bank.BankingPromptSwitcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class BankingPlanSolveAgent extends PlanningAgent {

    public BankingPlanSolveAgent(AgentContext context) {
        super(context);

        // 重写系统提示词，添加银行分析专用提示词
        String bankingPrompt = BankingPromptSwitcher.getBankingPrompt(context.getQuery(), "plan_solve");
        String originalPrompt = getSystemPrompt();
        setSystemPrompt(bankingPrompt + "\n\n" + originalPrompt);

        // 重写下一步提示词
        String originalNextPrompt = getNextStepPrompt();
        setNextStepPrompt(bankingPrompt + "\n\n" + originalNextPrompt);

        log.info("{} 初始化银行数据分析Plan-Solve Agent", context.getRequestId());
    }

    @Override
    public String run(String request) {
        log.info("{} Banking Plan-Solve Agent开始执行", getContext().getRequestId());

        // 检查是否有银行分析工具
        BaseTool bankingTool = getContext().getToolCollection().getTool("bank_data_analysis");
        if (bankingTool == null) {
            log.warn("{} 未找到银行分析工具，使用默认规划流程", getContext().getRequestId());
            return super.run(request);
        }

        // 添加银行数据分析特定的前置提示
        String bankingRequest = """  
            银行数据分析任务执行计划：  
            1. 获取数据库schema信息  
            2. 基于用户问题进行多维度分析规划  
            3. 并发生成SQL查询语句  
            4. 并发执行SQL获取真实数据  
            5. 基于分析类型执行相应分析逻辑  
            6. 生成ECharts图表配置  
            7. 输出最终分析结果  
              
            用户原始请求：  
            """ + request;

        return super.run(bankingRequest);
    }

    @Override
    public String act() {
        // 先执行父类的act方法
        String result = super.act();

        // 检查是否需要使用银行分析工具
        if (shouldUseBankingTool(result)) {
            log.info("{} 检测到需要使用银行分析工具", getContext().getRequestId());

            BaseTool bankingTool = getContext().getToolCollection().getTool("bank_data_analysis");
            if (bankingTool != null) {
                // 准备银行分析工具参数
                Map<String, Object> toolParams = prepareBankingToolParams();
                Object analysisResult = bankingTool.execute(toolParams);

                // 将分析结果添加到记忆中
                getMemory().addMessage(Message.assistantMessage(
                        "银行数据分析结果：" + analysisResult.toString(), null));

                return analysisResult.toString();
            }
        }

        return result;
    }

    private Map<String, Object> prepareBankingToolParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("query", getContext().getQuery());

        // 根据查询内容确定分析类型
        String analysisType = "data_query";
        if (getContext().getQuery().contains("归因分析")) {
            analysisType = "attribution_analysis";
        } else if (getContext().getQuery().contains("数据洞察")) {
            analysisType = "data_insight";
        } else if (getContext().getQuery().contains("报告")) {
            analysisType = "report_generation";
        }

        params.put("analysis_type", analysisType);
        return params;
    }
    private boolean shouldUseBankingTool(String task) {
        return task.contains("数据分析") || task.contains("SQL") ||
                task.contains("schema") || task.contains("归因") ||
                task.contains("洞察") || task.contains("报告");
    }
}