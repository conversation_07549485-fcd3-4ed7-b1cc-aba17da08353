package com.suite.chatdatabi.utils;


public class ResultTool {
    public static <T> JsonResult<T> success() {
        return new JsonResult<T>(true);
    }

    public static <T> JsonResult<T> success(T data) {
        return new JsonResult<T>(true, data);
    }

    public static <T> JsonResult<T> fail() {
        return new JsonResult<T>(false);
    }

    public static <T> JsonResult<T> fail(ResultCode resultEnum) {
        return new JsonResult<T>(false, resultEnum);
    }
    public static <T> JsonResult<T> fail(boolean success,T data) {
        return new JsonResult<T>(false, data);
    }
}
