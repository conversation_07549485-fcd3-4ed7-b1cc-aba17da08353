import React, { ReactNode } from 'react';
import { <PERSON>ton, Tooltip, message } from 'antd';
import { CheckCircleFilled, CloseCircleFilled, CopyOutlined } from '@ant-design/icons';
import { SqlInfoType } from '../../common/type';
import { PREFIX_CLS } from '../../common/constants';
import { format } from 'sql-formatter';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import classNames from 'classnames';

const prefixCls = `${PREFIX_CLS}-item`;

type Props = {
  sqlInfo: SqlInfoType;
  sqlTimeCost?: number;
  executeErrorMsg: string;
  isDeveloper?: boolean;
};

const GenerateSqlTip: React.FC<Props> = ({
  sqlInfo,
  sqlTimeCost,
  executeErrorMsg,
  isDeveloper,
}) => {
  const handleCopy = (_: string, result: any) => {
    result ? message.success('复制SQL成功', 1) : message.error('复制SQL失败', 1);
  };

  // 如果没有最终执行SQL，不显示组件
  if (!sqlInfo.querySQL) {
    return null;
  }

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
          </div>
        </div>
        {tipNode && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  // 如果有错误信息，显示失败状态
  if (executeErrorMsg) {
    return getNode(
      <>
        生成SQL失败
        {!!sqlTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {sqlTimeCost}ms)</span>
        )}
      </>,
      <div className={`${prefixCls}-sql-item`}>
        <div className={`${prefixCls}-sql-item-content`}>
          <div className={`${prefixCls}-sql-item-text`}>{executeErrorMsg}</div>
        </div>
      </div>,
      true
    );
  }

  const tipNode = (
    <div className={`${prefixCls}-tip`}>
      <div className={`${prefixCls}-sql-container`}>
        {/* 深灰色标题栏，右侧放复制按钮 */}
        <div className={`${prefixCls}-sql-header`}>
          <span className={`${prefixCls}-sql-title`}></span>
          <CopyToClipboard
            text={format(sqlInfo.querySQL)}
            onCopy={(text, result) => handleCopy(text, result)}
          >
            <Tooltip title="复制代码">
              <Button
                type="text"
                icon={<CopyOutlined />}
                className={`${prefixCls}-sql-copy-btn`}
              />
            </Tooltip>
          </CopyToClipboard>
        </div>
        {/* 淡灰色SQL内容区域 */}
        <div className={`${prefixCls}-sql-content`}>
          <SyntaxHighlighter
            language="sql"
            style={solarizedlight}
            customStyle={{
              margin: 0,
              padding: '12px',
              backgroundColor: '#f8f9fa',
              border: 'none',
              borderRadius: 0,
            }}
          >
            {format(sqlInfo.querySQL)}
          </SyntaxHighlighter>
        </div>
      </div>
    </div>
  );

  return getNode(
    <>
      生成SQL
      {!!sqlTimeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {sqlTimeCost}ms)</span>
      )}
    </>,
    tipNode
  );
};

export default GenerateSqlTip;
