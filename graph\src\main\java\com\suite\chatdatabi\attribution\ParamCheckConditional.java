package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.EdgeAction;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;

public class ParamCheckConditional implements EdgeAction {
    @Override
    public String apply(OverAllState state) throws Exception {

        if (state.value("queryText").get() == null
                || state.value("queryText").get().equals("")
        ) {
            return "query_missing";
        }
        if (state.value("segmentResult").get() == null
                || state.value("segmentResult").get().equals("[]")
        ) {
            return "segment_missing";
        }
        if (state.value("relationshipResult").get() == null
                || state.value("relationshipResult").get().equals("[]")
        ) {
            return "relationship_missing";
        }
        if (state.value("schemaResult").get() == null
                || state.value("schemaResult").get().equals("[]")
        ) {
            return "schema_missing";
        }
        return "ok";
    }
}
