import React, { useState } from 'react';
import { Button, Space } from 'antd';
import ChatItem from './index';

// 模拟数据
const mockMsgData = {
  queryMode: 'STRUCTURED_ANALYTICS',
  queryColumns: [
    { name: '日期', nameEn: 'date', showType: 'DATE', type: 'DATE', bizName: 'date' },
    { name: '销售额', nameEn: 'sales', showType: 'NUMBER', type: 'NUMBER', bizName: 'sales' },
    { name: '地区', nameEn: 'region', showType: 'CATEGORY', type: 'STRING', bizName: 'region' }
  ],
  queryResults: [
    { date: '2024-01-01', sales: 1000, region: '北京' },
    { date: '2024-01-02', sales: 1200, region: '上海' },
    { date: '2024-01-03', sales: 800, region: '广州' }
  ],
  queryState: 'SUCCESS',
  textResult: '查询结果显示销售数据',
  chatContext: {
    id: 1,
    dimensions: ['地区'],
    metrics: ['销售额'],
    sqlInfo: {
      querySQL: 'SELECT date, sales, region FROM sales_table WHERE date >= "2024-01-01"',
      correctedSQL: 'SELECT date, sales, region FROM sales_table WHERE date >= "2024-01-01"'
    }
  }
};

const mockParseInfos = [
  {
    id: 1,
    dimensions: ['地区'],
    metrics: ['销售额'],
    dimensionFilters: [],
    dateInfo: { startDate: '2024-01-01', endDate: '2024-01-03' },
    entityInfo: { name: '销售表' },
    sqlInfo: {
      querySQL: 'SELECT date, sales, region FROM sales_table WHERE date >= "2024-01-01"',
      correctedSQL: 'SELECT date, sales, region FROM sales_table WHERE date >= "2024-01-01"'
    }
  }
];

const ThinkingChainDemo: React.FC = () => {
  const [showNewMessage, setShowNewMessage] = useState(false);
  const [showExistingMessage, setShowExistingMessage] = useState(false);

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>思维链渲染演示</h2>

      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}>
        <h4>测试要点：</h4>
        <ul>
          <li>新消息：Loading应该在"意图分类中"后面显示</li>
          <li>新消息：组件应该按1秒间隔依次显示，并自动滚动</li>
          <li>已有消息：应该立即显示所有组件，无动画</li>
        </ul>
      </div>

      <Space style={{ marginBottom: '20px' }}>
        <Button
          type="primary"
          onClick={() => {
            setShowNewMessage(false);
            setShowExistingMessage(false);
            setTimeout(() => setShowNewMessage(true), 100);
          }}
        >
          演示新消息思维链（应该有1秒间隔）
        </Button>
        
        <Button
          onClick={() => {
            setShowNewMessage(false);
            setShowExistingMessage(false);
            setTimeout(() => setShowExistingMessage(true), 100);
          }}
        >
          演示已有消息（应该立即显示所有组件）
        </Button>
        
        <Button 
          onClick={() => {
            setShowNewMessage(false);
            setShowExistingMessage(false);
          }}
        >
          清空
        </Button>
      </Space>

      {showNewMessage && (
        <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px', marginBottom: '20px' }}>
          <h3>新消息 - 带思维链动画</h3>
          <ChatItem
            msg="查询最近三天的销售数据"
            conversationId={1}
            questionId={undefined}
            agentId={1}
            parseInfos={mockParseInfos}
            msgData={undefined} // 新消息没有msgData
            intentType="数据查询"
            difyParamsFromPageQuery={{
              provider: 'DIFY',
              baseUrl: 'http://localhost:3000',
              apiKey: 'test-key',
              data_query_type: '数据查询'
            }}
            onMsgDataLoaded={(data, valid) => {
              console.log('新消息数据加载完成:', data, valid);
            }}
            onUpdateMessageScroll={() => {
              console.log('滚动更新');
            }}
          />
        </div>
      )}

      {showExistingMessage && (
        <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px' }}>
          <h3>已有消息 - 无思维链动画</h3>
          <ChatItem
            msg="查询最近三天的销售数据"
            conversationId={1}
            questionId={1}
            agentId={1}
            parseInfos={mockParseInfos}
            msgData={mockMsgData} // 已有消息有msgData
            intentType="数据查询"
            difyParamsFromPageQuery={{
              provider: 'DIFY',
              baseUrl: 'http://localhost:3000',
              apiKey: 'test-key',
              data_query_type: '数据查询'
            }}
            onMsgDataLoaded={(data, valid) => {
              console.log('已有消息数据加载完成:', data, valid);
            }}
            onUpdateMessageScroll={() => {
              console.log('滚动更新');
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ThinkingChainDemo;
