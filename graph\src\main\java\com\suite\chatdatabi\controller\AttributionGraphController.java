package com.suite.chatdatabi.controller;


import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.RunnableConfig;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.exception.GraphRunnerException;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.suite.chatdatabi.auth.api.authentication.utils.UserHolder;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.process.GraphProcess;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/graph")
public class AttributionGraphController {

    private final GraphProcess graphProcess = new GraphProcess();

    private final CompiledGraph compiledGraph;

    public AttributionGraphController(@Qualifier("attributionGraph") StateGraph stateGraph) throws GraphStateException {
        this.compiledGraph = stateGraph.compile();
    }


    @PostMapping("attribution")
    public Flux<ServerSentEvent<String>> attribution(@RequestBody ChatParseReq chatParseReq, HttpServletRequest request,
                                                     HttpServletResponse response) throws GraphRunnerException {
        User user = UserHolder.findUser(request, response);

        Sinks.Many<ServerSentEvent<String>> sink = Sinks.many().unicast().onBackpressureBuffer();

        RunnableConfig runnableConfig = RunnableConfig.builder()
                .threadId(UUID.randomUUID().toString())
                .build();
       Map<String, Object> querymap = Map.of("queryText", chatParseReq.getQueryText(),
                                            "agentId",chatParseReq.getAgentId(),
                                            "chatId",chatParseReq.getChatId(),
                                            "user",user);
        AsyncGenerator<NodeOutput> stream = compiledGraph.stream(querymap, runnableConfig);
        graphProcess.processStream(stream,sink);
        return sink.asFlux();
    }

}
