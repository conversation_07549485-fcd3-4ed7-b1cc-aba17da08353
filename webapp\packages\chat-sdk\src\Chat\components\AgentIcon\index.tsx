import React, { useMemo } from 'react';
import classNames from 'classnames';
import styles from './style.module.less';

// 直接导入 SVG 文件作为 URL（按顺序导入现有的图标）
import agentIcon1 from '../../../assets/agent-icon/agentIcon1.svg';
import agentIcon2 from '../../../assets/agent-icon/agentIcon2.svg';
import agentIcon3 from '../../../assets/agent-icon/agentIcon3.svg';
import agentIcon4 from '../../../assets/agent-icon/agentIcon4.svg';
import agentIcon5 from '../../../assets/agent-icon/agentIcon5.svg';
import agentIcon6 from '../../../assets/agent-icon/agentIcon6.svg';
import agentIcon7 from '../../../assets/agent-icon/agentIcon7.svg';
import agentIcon8 from '../../../assets/agent-icon/agentIcon8.svg';
import agentIcon9 from '../../../assets/agent-icon/agentIcon9.svg';
import agentIcon10 from '../../../assets/agent-icon/agentIcon10.svg';
import agentIcon11 from '../../../assets/agent-icon/agentIcon11.svg';
import agentIcon13 from '../../../assets/agent-icon/agentIcon13.svg';

// SVG 图标路径数组（按顺序排列，总共12个图标）
const iconPaths = [
  agentIcon1,   // 索引 0 -> Agent ID 1, 13, 25, ...
  agentIcon2,   // 索引 1 -> Agent ID 2, 14, 26, ...
  agentIcon3,   // 索引 2 -> Agent ID 3, 15, 27, ...
  agentIcon4,   // 索引 3 -> Agent ID 4, 16, 28, ...
  agentIcon5,   // 索引 4 -> Agent ID 5, 17, 29, ...
  agentIcon6,   // 索引 5 -> Agent ID 6, 18, 30, ...
  agentIcon7,   // 索引 6 -> Agent ID 7, 19, 31, ...
  agentIcon8,   // 索引 7 -> Agent ID 8, 20, 32, ...
  agentIcon9,   // 索引 8 -> Agent ID 9, 21, 33, ...
  agentIcon10,  // 索引 9 -> Agent ID 10, 22, 34, ...
  agentIcon11,  // 索引 10 -> Agent ID 11, 23, 35, ...
  agentIcon13,  // 索引 11 -> Agent ID 12, 24, 36, ...
];

interface AgentIconProps {
  agentId: number;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 根据 agentId 使用取模运算循环获取对应的图标路径
 * 这是开发中常见的数组循环取值方法（模运算）
 *
 * 示例：
 * - Agent ID 1 -> 索引 0 (agentIcon1)
 * - Agent ID 2 -> 索引 1 (agentIcon2)
 * - ...
 * - Agent ID 12 -> 索引 11 (agentIcon13)
 * - Agent ID 13 -> 索引 0 (agentIcon1) 重新开始循环
 * - Agent ID 14 -> 索引 1 (agentIcon2)
 *
 * @param agentId 智能体ID（从1开始）
 * @returns 对应的图标路径
 */
const getAgentIconPath = (agentId: number): string => {
  // 使用取模运算实现循环：(agentId - 1) % 数组长度
  // agentId - 1 是因为 agentId 从1开始，但数组索引从0开始
  const index = (agentId - 1) % iconPaths.length;

  // console.log(`Agent ID ${agentId} -> 数组索引 ${index} -> 图标: ${iconPaths[index]}`);

  return iconPaths[index];
};

const AgentIcon: React.FC<AgentIconProps> = ({ agentId, className, style }) => {
  const iconPath = useMemo(() => getAgentIconPath(agentId), [agentId]);

  return (
    <span className={classNames(styles.agentIcon, className)} style={style}>
      <img src={iconPath} alt={`Agent ${agentId}`} style={{ width: '100%', height: '100%' }} />
    </span>
  );
};

export default AgentIcon;
