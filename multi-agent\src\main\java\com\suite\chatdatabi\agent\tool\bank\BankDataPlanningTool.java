package com.suite.chatdatabi.agent.tool.bank;


import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.dto.Message;
import com.suite.chatdatabi.agent.llm.LLM;
import com.suite.chatdatabi.agent.tool.BaseTool;
import com.suite.chatdatabi.agent.tool.mcp.McpTool;
import com.suite.chatdatabi.agent.util.SpringContextHolder;
import com.suite.chatdatabi.config.GenieConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Data
public class BankDataPlanningTool implements BaseTool {

    private AgentContext agentContext;
    private McpTool mcpTool;
    private GenieConfig genieConfig;
    private ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public String getName() {
            return "bank_data_planning";
    }

    @Override
    public String getDescription() {
        return "银行数据问题拆分工具，基于Schema信息对问题进行维度拆分";
    }


    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        Map<String, Object> questionParam = new HashMap<>();
        questionParam.put("type", "string");
        questionParam.put("description", "用户问题");
        properties.put("question", questionParam);

        Map<String, Object> schemaParam = new HashMap<>();
        schemaParam.put("type", "string");
        schemaParam.put("description", "schema信息");
        properties.put("schema_info", schemaParam);

        Map<String, Object> questionTypeParam = new HashMap<>();
        questionTypeParam.put("type", "string");
        questionTypeParam.put("description", "问题类型：数据查询/归因分析/报告生成/洞察分析");
        properties.put("question_type", questionTypeParam);

        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("question", "schema_info", "question_type"));
        return parameters;
    }

    @Override
    public Object execute(Object input) {
        try {
            Map<String, Object> params = (Map<String, Object>) input;
            String originalQuestion = (String) params.get("question");
            String schemaInfo = (String) params.get("schema_info");
            String questionType = (String) params.get("question_type");

            List<String> dimensionQuestions = planMultiDimensionalAnalysis(originalQuestion, schemaInfo,questionType);

            // 返回格式化的字符串结果，而不是Map
            StringBuilder result = new StringBuilder();
//            result.append("银行数据分析规划完成：\n");
//            result.append("原始问题：").append(originalQuestion).append("\n");
//            result.append("问题类型：").append(questionType).append("\n");
//            result.append("维度分析结果（共").append(dimensionQuestions.size()).append("个维度）：\n");

            for (int i = 0; i < dimensionQuestions.size(); i++) {
                if(dimensionQuestions.get(i).contains("[无需拆分]")){
                    result.append(originalQuestion);
                }else {
                    result.append(i + 1).append(": ").append(dimensionQuestions.get(i)).append("\n\n");
                }
//                result.append(i + 1).append(". ").append(dimensionQuestions.get(i)).append("\n");
            }

            return result.toString();
        } catch (Exception e) {
            log.error("{} bank_data_planning error", agentContext != null ? agentContext.getRequestId() : "unknown", e);
            return "银行数据规划工具执行失败：" + e.getMessage();
        }
    }

    private List<String> planMultiDimensionalAnalysis(String question, String schemaInfo,String questionType) {
        List<String> analysisQuestions = new ArrayList<>();

        try {
//            String planningPrompt = String.format(
//                    "基于以下数据库schema信息：%s\n" +
//                            "用户问题：%s\n" +
//                            "问题类型：%s\n" +
//                            "请智能决定是否需要拆分问题。\n\n" +
//
//                            "## 拆分判断标准：\n" +
//                            "1. 简单数据查询：如果问题可以通过单一查询解决且schema完全覆盖，直接输出原问题\n" +
//                            "2. 复杂数据查询：如果问题涉及多个维度分析或复杂业务逻辑，拆分为2-3个子问题\n" +
//                            "3. 深度分析问题：如果问题需要多层次分析或跨实体关联，可拆分为3-5个子问题\n" +
//                            "4. 每个子问题必须有独立的分析价值和实际业务意义\n\n" +
//
//                            "## 拆分策略：\n" +
//                            "### 数据查询类问题：\n" +
//                            "- 基础查询：单表或简单关联查询，无需拆分\n" +
//                            "- 复合查询：涉及多表关联、聚合计算，拆分为2-3个逻辑步骤\n" +
//                            "- 分析查询：需要对比、趋势、异常检测，拆分为3-4个分析维度\n\n" +
//
//                            "### 深度分析类问题：\n" +
//                            "- 现状分析：基于schema主要实体的当前状态查询\n" +
//                            "- 趋势分析：利用时间字段的历史数据变化分析\n" +
//                            "- 对比分析：基于分类字段的横向或纵向对比\n" +
//                            "- 关联分析：挖掘实体间的关联关系和影响因素\n" +
//                            "- 预测建议：基于历史数据的趋势预测（可选）\n\n" +
//
//                            "## 拆分要求：\n" +
//                            "1. 严格基于schema中实际存在的表、字段和关系\n" +
//                            "2. 每个子问题必须能独立执行并产生有意义的结果\n" +
//                            "3. 子问题间有逻辑递进关系，共同解决用户核心问题\n" +
//                            "4. 简单问题1个，复杂问题2-3个，深度分析最多5个\n" +
//                            "5. 优先考虑实际业务场景的分析需求\n" +
//                            "6. 避免过度拆分，确保每个子问题都有实际价值\n\n" +
//
//                            "## 输出要求：\n" +
//                            "- 简单查询：直接输出原问题\n" +
//                            "- 需要拆分：每行一个子问题，无前缀编号\n" +
//                            "- 不输出分析过程或解释\n" +
//                            "- 只输出最终问题列表\n\n",
//                    schemaInfo, question, questionType);
            String planningPrompt = String.format(
                    "基于以下数据库schema信息：%s\n" +
                            "用户问题：%s\n" +
                            "严格以Schema为边界，智能判断是否需要拆分问题。如需拆分则最多拆分为5个子问题。\n" +
                            "要求：\n" +
                            "1. 严格基于schema中实际存在的表、字段和关系进行拆分\n" +
                            "2. 每个子问题必须具有独立的分析意义\n" +
                            "3. 每个子问题都能通过现有schema独立生成SQL语句\n" +
                            "4. 子问题之间应该互补而非重复\n" +
                            "5. 严禁生成schema中不存在的表、字段或关系相关的问题\n\n" +
                            "输出要求：\n" +
                            "- 如果无需拆分：直接输出原问题\n" +
                            "- 如果需要拆分：仅输出子问题列表，格式如下：\n" +
                            "1. [可生成SQL的子问题描述]\n" +
                            "2. [可生成SQL的子问题描述]\n" +
                            "...\n\n" +
                            "注意：只返回拆分后的子问题，不要输出任何解释、分析或多余内容。",
                    schemaInfo, question
            );
            // 创建LLM实例
            GenieConfig genieConfig = SpringContextHolder.getApplicationContext().getBean(GenieConfig.class);
            LLM llm = new LLM(genieConfig.getPlannerModelName(), "");

            // 构建消息
            Message planningMessage = Message.userMessage(planningPrompt, null);

            // 调用LLM进行分析
            CompletableFuture<String> future = llm.ask(
                    agentContext,
                    Collections.singletonList(planningMessage),
                    Collections.emptyList(),
                    false,
                    0.1
            );

            String llmResponse = future.get();

            // 解析LLM响应，提取分析问题
            analysisQuestions = parseAnalysisQuestions(llmResponse);

            // 如果解析失败或结果为空，使用默认维度
            if (analysisQuestions.isEmpty()) {
                log.warn("LLM维度分析解析失败，使用默认维度");
                analysisQuestions.add(question);
            }

            log.info("LLM问题拆分结果：{}", analysisQuestions);

        } catch (Exception e) {
            log.error("调用LLM进行维度分析失败", e);
            // 异常情况下使用默认维度
            analysisQuestions.add(question);
        }

        return analysisQuestions;
    }
    /**
     * 解析LLM响应，提取分析问题
     */
    private List<String> parseAnalysisQuestions(String llmResponse) {
        List<String> questions = new ArrayList<>();

        if (llmResponse == null || llmResponse.trim().isEmpty()) {
            return questions;
        }

        // 使用正则表达式解析格式化的响应
        String[] lines = llmResponse.split("\n");
        Pattern pattern = Pattern.compile("^\\d+\\.\\s*(.+)$");

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            Matcher matcher = pattern.matcher(line);
            if (matcher.find()) {
                String question = matcher.group(1).trim();
                if (!question.isEmpty()) {
                    questions.add(question);
                }
            } else if (line.contains("：") || line.contains(":")) {
                // 备用解析方式，直接提取包含冒号的行
                questions.add(line);
            }
        }

        return questions;
    }
}