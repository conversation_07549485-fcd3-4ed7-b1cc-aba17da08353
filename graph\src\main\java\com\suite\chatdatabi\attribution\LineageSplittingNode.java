package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.streaming.StreamingChatGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.PromptTemplate;
import reactor.core.publisher.Flux;

import java.util.Map;
@Slf4j
public class LineageSplittingNode implements NodeAction {

    private ChatClient chatClient;

    private static final PromptTemplate DEFAULT_PROMPT_TEMPLATE = new PromptTemplate(
            """
                    你是一名擅长根据血缘关系进行归因分析的数据分析师。你的任务是根据提供的血缘的指标层级关系树、表的schema信息以及用户的问题，生成用于进行血缘分析的数据查询的nl2sql问题。
                    以下是血缘的指标层级关系树：
                    <hierarchy_tree>
                    {HIERARCHY_TREE}
                    </hierarchy_tree>
                    以下是表的schema信息：
                    <schema_info>
                    {SCHEMA_INFO}
                    </schema_info>
                    这是用户的问题：
                    <user_question>
                    {USER_QUESTION}
                    </user_question>
                    在生成nl2sql问题时，请遵循以下规则：
                    - 如果用户的问题包含年、季度、月、日时间范围，需要对时间进行更精细的范围查询。具体规则为：若用户问题的时间范围是年，生成的时间范围应为季度；若用户问题的时间范围是季度，生成的时间范围应为月度；若用户问题的时间范围是日，生成的时间范围应为小时。
                    - 生成的问题数量 = 不同维度数 * 不同指标数。
                    - 请只生成最终结果，不要输出其他额外内容。
                    - 请将最终结果以字符串数组格式输出，如 ["问题1", "问题2", "问题3"]。
                    """
    );


    public LineageSplittingNode(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {

//        throw new Exception("LineageSplittingNode is not supported.");

        Object queryText =  state.value("queryText").orElse( null);
        Object schemaResult =  state.value("schemaResult").orElse( null);
        Object relationshipResult =  state.value("relationshipResult").orElse( null);

        Flux<ChatResponse> streamResult = this.chatClient.prompt().user(
                (user) ->
                        user.text(DEFAULT_PROMPT_TEMPLATE.getTemplate())
                                .param("SCHEMA_INFO", schemaResult)
                                .param("HIERARCHY_TREE", relationshipResult)
                                .param("USER_QUESTION", queryText)
                                ).stream()
                .chatResponse();
        AsyncGenerator<? extends NodeOutput> generator = StreamingChatGenerator.builder()
                .startingNode("lineage_splitting_stream")
                .startingState(state)
                .mapResult(response -> {
                    String text = response.getResult().getOutput().getText();
                    return Map.of("lineageSplittingResult", text);
                }).build(streamResult);
        return Map.of("lineageSplittingResult",generator);
    }
}
