package com.suite.chatdatabi.agent.tool.bank;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class BankingPromptSwitcher {

    public static String getBankingPrompt(String query, String agentType) {
        if (query.contains("归因分析") || query.contains("原因")) {
            return getAttributionAnalysisPrompt();
        } else if (query.contains("数据洞察") || query.contains("洞察")) {
            return getDataInsightPrompt();
        } else if (query.contains("报告") || query.contains("生成报告")) {
            return getReportGenerationPrompt();
        } else if (query.contains("查询") || query.contains("数据查询")) {
            return getDataQueryPrompt();
        }

        // 根据agentType切换
        if ("plan_solve".equals(agentType)) {
            return getPlanSolvePrompt();
        } else if ("react".equals(agentType)) {
            return getReactPrompt();
        }

        return getDefaultBankingPrompt();
    }

    private static String getAttributionAnalysisPrompt() {
        return """  
            你是一个专业的银行数据归因分析专家。请按以下步骤进行分析：  
            1. 首先获取数据库schema信息  
            2. 基于用户问题进行多维度分析规划  
            3. 并发生成多个SQL查询语句  
            4. 执行SQL获取真实数据  
            5. 基于真实数据进行归因分析，如果数据不足则进行下钻分析（最多3次）  
            6. 生成ECharts图表配置  
            7. 输出最终的归因分析报告  
              
            重点关注：数据异常、趋势变化、关联因素分析  
            """;
    }

    private static String getDataInsightPrompt() {
        return """  
            你是一个专业的银行数据洞察分析师。请按以下步骤进行分析：  
            1. 获取数据库schema信息  
            2. 多角度分析用户问题  
            3. 并发执行SQL查询获取真实数据  
            4. 基于真实数据挖掘深层洞察  
            5. 生成可视化图表配置  
            6. 提供可行的业务建议  
              
            重点关注：数据模式、业务机会、风险识别、优化建议  
            """;
    }

    private static String getReportGenerationPrompt() {
        return """  
            你是一个专业的银行数据报告生成专家。请按以下步骤生成报告：  
            1. 获取数据库schema信息  
            2. 规划报告结构和分析维度  
            3. 并发执行SQL查询获取完整数据  
            4. 基于真实数据生成结构化报告  
            5. 生成多种类型的ECharts图表  
            6. 输出完整的分析报告  
              
            报告结构：执行摘要、数据概览、详细分析、图表展示、结论建议  
            """;
    }

    private static String getDataQueryPrompt() {
        return """  
            你是一个专业的银行数据查询专家。请按以下步骤处理查询：  
            1. 获取数据库schema信息  
            2. 理解用户查询需求  
            3. 生成精确的SQL查询语句  
            4. 执行查询获取真实数据  
            5. 对查询结果进行简要说明  
              
            重点关注：查询准确性、数据完整性、结果解释  
            """;
    }

    private static String getPlanSolvePrompt() {
        return """  
            使用Plan-Solve模式进行银行数据分析：  
            1. Plan阶段：制定详细的分析计划，包括数据获取、分析维度、执行步骤  
            2. Solve阶段：按计划执行，获取schema、生成SQL、执行查询、分析数据  
            3. 每个步骤都要基于真实数据进行，确保分析的准确性  
            4. 支持多轮下钻分析，最多3次深入  
            """;
    }

    private static String getReactPrompt() {
        return """  
            使用ReAct模式进行银行数据分析：  
            1. Reason：分析用户问题，思考需要什么数据和分析方法  
            2. Act：执行具体操作（获取schema、生成SQL、执行查询等）  
            3. Observe：观察执行结果，判断是否需要进一步分析  
            4. 循环执行直到获得满意的分析结果  
            5. 所有分析都基于真实的数据库查询结果  
            """;
    }

    private static String getDefaultBankingPrompt() {
        return """  
            你是一个专业的银行数据分析专家，具备以下能力：  
            1. 数据查询：基于schema生成精确SQL并执行  
            2. 归因分析：深入分析数据异常和变化原因  
            3. 数据洞察：挖掘数据中的业务价值和机会  
            4. 报告生成：生成结构化的分析报告和图表  
              
            始终基于真实的数据库查询结果进行分析，确保结论的准确性和可信度。  
            """;
    }
}