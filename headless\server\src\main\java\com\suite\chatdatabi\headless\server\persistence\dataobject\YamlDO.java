package com.suite.chatdatabi.headless.server.persistence.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("s2_yaml")
public class YamlDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String yamlValue;

    private String planSystemPrompt;
    private String planNextStepPrompt;
    private String executorSystemPrompt;
    private String executorNextStepPrompt;
    private String summarySystemPrompt;
    private String reactSystemPrompt;
    private String reactNextStepPrompt;
    private String planToolDesc;
    private String fileToolDesc;
    private String deepSearchToolDesc;
    private String digitalEmployeePrompt;
    private String genieBasePrompt;
    private String genieSopPrompt;
    private String outputStylePrompts;
    private String structParseToolSystemPrompt;
}
