# AgentIcon 组件

一个智能体图标组件，根据智能体ID循环使用不同的SVG图标，替换原有的固定 RobotOutlined 图标。

## 功能特性

- 🎨 **丰富的图标**: 支持13种不同的SVG图标，使界面更加丰富多彩
- 🔄 **循环取用**: 当智能体数量超过图标数量时，自动循环使用图标
- 🛡️ **强大的错误处理**: 多层回退机制，确保组件始终能正常显示
- 📱 **响应式**: 支持自定义样式和尺寸
- 🚀 **即插即用**: 完全兼容原有的 RobotOutlined 图标接口

## 使用方法

```tsx
import AgentIcon from '../components/AgentIcon';

// 基本使用
<AgentIcon agentId={1} />

// 自定义样式
<AgentIcon 
  agentId={5} 
  className="custom-icon" 
  style={{ width: '24px', height: '24px' }} 
/>
```

## 图标映射规则

- Agent ID 1 → agentIcon1.svg
- Agent ID 2 → agentIcon2.svg
- ...
- Agent ID 13 → agentIcon13.svg
- Agent ID 14 → agentIcon1.svg (循环开始)
- Agent ID 15 → agentIcon2.svg
- 以此类推...

## Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| agentId | number | ✅ | - | 智能体ID，用于确定使用哪个图标 |
| className | string | ❌ | - | 自定义CSS类名 |
| style | React.CSSProperties | ❌ | - | 自定义内联样式 |

## 图标文件位置

所有SVG图标文件位于：`src/assets/agent-icon/`

- agentIcon1.svg
- agentIcon2.svg
- ...
- agentIcon13.svg

## 注意事项

1. 确保所有SVG图标文件都存在于指定目录
2. 图标文件命名必须遵循 `agentIcon{数字}.svg` 的格式
3. 组件会自动处理图标加载失败的情况，回退到第一个图标
