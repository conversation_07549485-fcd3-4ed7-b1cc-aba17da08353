package com.suite.chatdatabi.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class SqlCharacterNormalizer {

    // 全角到半角字符映射表
    private static final Map<Character, Character> FULLWIDTH_TO_HALFWIDTH = new HashMap<>();

    static {
        // 常见的全角标点符号映射
        FULLWIDTH_TO_HALFWIDTH.put('（', '('); // \uff08 -> \u0028
        FULLWIDTH_TO_HALFWIDTH.put('）', ')'); // \uff09 -> \u0029
        FULLWIDTH_TO_HALFWIDTH.put('，', ','); // \uff0c -> \u002c
        FULLWIDTH_TO_HALFWIDTH.put('。', '.'); // \u3002 -> \u002e
        FULLWIDTH_TO_HALFWIDTH.put('；', ';'); // \uff1b -> \u003b
        FULLWIDTH_TO_HALFWIDTH.put('：', ':'); // \uff1a -> \u003a
        FULLWIDTH_TO_HALFWIDTH.put('？', '?'); // \uff1f -> \u003f
        FULLWIDTH_TO_HALFWIDTH.put('！', '!'); // \uff01 -> \u0021
        FULLWIDTH_TO_HALFWIDTH.put('"', '"'); // \u201c -> \u0022
        FULLWIDTH_TO_HALFWIDTH.put('"', '"'); // \u201d -> \u0022


        // 全角数字和字母
        for (int i = 0; i <= 9; i++) {
            FULLWIDTH_TO_HALFWIDTH.put((char) ('\uff10' + i), (char) ('0' + i));
        }
        for (int i = 0; i < 26; i++) {
            FULLWIDTH_TO_HALFWIDTH.put((char) ('\uff21' + i), (char) ('A' + i)); // 大写
            FULLWIDTH_TO_HALFWIDTH.put((char) ('\uff41' + i), (char) ('a' + i)); // 小写
        }
    }

    /**
     * 将SQL中的全角字符转换为半角字符
     */
    public static String normalizeSql(String sql) {
        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        StringBuilder normalized = new StringBuilder();
        for (char c : sql.toCharArray()) {
            Character halfWidth = FULLWIDTH_TO_HALFWIDTH.get(c);
            if (halfWidth != null) {
                normalized.append(halfWidth);
                log.debug("Normalized fullwidth character '{}' to '{}'", c, halfWidth);
            } else {
                normalized.append(c);
            }
        }

        String result = normalized.toString();
        if (!sql.equals(result)) {
            log.info("SQL character normalization applied");
        }

        return result;
    }

    /**
     * 检查SQL是否包含全角字符
     */
    public static boolean containsFullwidthCharacters(String sql) {
        if (StringUtils.isBlank(sql)) {
            return false;
        }

        return sql.chars().anyMatch(c -> FULLWIDTH_TO_HALFWIDTH.containsKey((char) c));
    }
}
