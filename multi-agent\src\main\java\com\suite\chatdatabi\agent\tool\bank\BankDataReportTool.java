package com.suite.chatdatabi.agent.tool.bank;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.dto.File;
import com.suite.chatdatabi.agent.dto.Message;
import com.suite.chatdatabi.agent.llm.LLM;
import com.suite.chatdatabi.agent.tool.BaseTool;
import com.suite.chatdatabi.agent.tool.common.FileTool;
import com.suite.chatdatabi.agent.util.SpringContextHolder;
import com.suite.chatdatabi.config.GenieConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Data
public class BankDataReportTool implements BaseTool {
    private AgentContext agentContext;
    private EChartsRenderTool eChartsRenderTool;
    private LLM llm;

    @Override
    public String getName() {
        return "bank_data_report";
    }

    @Override
    public String getDescription() {
        return "基于真实数据库查询结果生成银行数据分析报告，包含ECharts图表";
    }

    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("type", "string");
        queryParam.put("description", "用户原始问题");
        properties.put("query", queryParam);

        Map<String, Object> analysisParam = new HashMap<>();
        analysisParam.put("type", "string");
        analysisParam.put("description", "数据分析总结");
        properties.put("analysis", analysisParam);

        Map<String, Object> formatParam = new HashMap<>();
        formatParam.put("type", "string");
        formatParam.put("description", "报告格式：markdown, html, excel");
        formatParam.put("default", "markdown");
        properties.put("format", formatParam);

        params.put("properties", properties);
        params.put("required", Arrays.asList("query", "sqlResults", "analysis"));

        return params;
    }
    @Override
    public Object execute(Object input) {
        try {
            // 使用 getAllToolResults() 获取所有工具执行结果
            Map<String, Object> toolResult = agentContext.getAllToolResults();
            log.info("{} 报告生成=====================获取所有工具执行结果: {}",
                    agentContext.getRequestId(), JSONObject.toJSONString(toolResult, true));

            // 验证必要的工具结果是否存在
            validateToolResults(toolResult);

            Map<String, Object> params = (Map<String, Object>) input;
            String query = (String) params.get("query");
            String analysis = (String) params.get("analysis");
            String format = (String) params.getOrDefault("format", "markdown");

            // 生成完整的银行数据分析报告
            String reportContent = generateEnhancedBankAnalysisReport(query, toolResult, analysis);

            // 生成多种格式的文件
            List<String> generatedFiles = generateMultipleFormatFiles(reportContent, query, format);

            // 发送任务完成结果
            sendTaskCompletionResult(generatedFiles);

            return "银行数据分析报告已生成完成，包含 " + generatedFiles.size() + " 个文件: " +
                    String.join(", ", generatedFiles);


        } catch (Exception e) {
            log.error("{} BankDataAnalysisReportTool error", agentContext.getRequestId(), e);
            return "报告生成失败: " + e.getMessage();
        }
    }

    /**
     * 验证工具结果的完整性
     */
    private void validateToolResults(Map<String, Object> toolResult) {
        if (toolResult == null || toolResult.isEmpty()) {
            log.warn("{} 工具执行结果为空，将生成基础报告", agentContext.getRequestId());
            return;
        }

        List<String> availableTools = new ArrayList<>(toolResult.keySet());
        log.info("{} 可用的工具结果: {}", agentContext.getRequestId(), availableTools);
    }


    /**
     * 生成增强版银行数据分析报告
     */
    private String generateEnhancedBankAnalysisReport(String query, Map<String, Object> toolResult, String analysis) {
        try {
            // 构建增强版报告提示词
            String llmPrompt = buildEnhancedReportPrompt(query, toolResult, analysis);

            // 创建消息
            Message userMessage = Message.userMessage(llmPrompt, null);

            // 调用LLM生成报告
            CompletableFuture<String> reportFuture = getLlm().ask(
                    agentContext,
                    Collections.singletonList(userMessage),
                    Collections.emptyList(),
                    false,
                    0.3  // 适当的温度值，保证创造性
            );

            // 获取LLM生成的报告内容
            String reportContent = reportFuture.get();

            // 增强报告内容，添加图表和数据可视化
            reportContent = enhanceReportWithCharts(reportContent, toolResult);

            log.info("{} LLM生成的银行数据分析报告完成", agentContext.getRequestId());

            return reportContent;

        } catch (Exception e) {
            log.error("{} 调用LLM生成报告失败", agentContext.getRequestId(), e);
            // 降级到原有的硬编码逻辑
            return generateFallbackReport(query, toolResult, analysis);
        }
    }

    /**
     * 构建增强版报告提示词
     */
    private String buildEnhancedReportPrompt(String query, Map<String, Object> toolResult, String analysis) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("# 角色\n");
        prompt.append("你是一个专业的银行数据分析专家，需要基于真实的工具执行结果生成完整的银行数据分析报告。\n\n");

        // 添加更详细的报告结构要求
        prompt.append("## 报告结构要求\n");
        prompt.append("请生成一份包含以下完整结构的银行数据分析报告：\n");
        prompt.append("1. **执行摘要** - 核心发现和关键指标概述\n");
        prompt.append("2. **数据概览** - 数据来源、时间范围、数据质量说明\n");
        prompt.append("3. **详细分析** - 分维度深入分析，包含数据表格和趋势分析\n");
        prompt.append("4. **风险评估** - 识别潜在风险点和合规性分析\n");
        prompt.append("5. **业务洞察** - 基于数据的业务建议和改进方案\n");
//        prompt.append("6. **附录** - 详细数据表格和技术说明\n\n");

        // 添加具体的数据分析要求
        prompt.append("## 数据分析要求\n");
        prompt.append("- 根据每个数据集的结构动态分析数据特征\n");
        prompt.append("- 为数值型数据生成趋势分析和统计摘要\n");
        prompt.append("- 为分类数据生成分布分析\n");
        prompt.append("- 识别数据集之间的关联关系\n");
        prompt.append("- 自动选择合适的图表类型展示数据\n\n");

        prompt.append("## 用户原始问题\n");
        prompt.append(query).append("\n\n");

        prompt.append("## 数据分析总体情况\n");
        prompt.append(analysis).append("\n\n");

        prompt.append("## 工具执行结果数据\n");
        prompt.append("以下是所有工具的执行结果，请基于这些真实数据进行分析：\n\n");

        // 分别展示每个工具的结果
        if (toolResult.containsKey("schema_info")) {
            prompt.append("### 数据库模式信息 (schema_tool)\n");
            prompt.append("```json\n");
            prompt.append(JSONObject.toJSONString(toolResult.get("schema_info"), true));
            prompt.append("\n```\n\n");
        }

//        if (toolResult.containsKey("nl2sql")) {
//            prompt.append("### SQL查询生成结果 (nl2sql_tool)\n");
//            prompt.append("```json\n");
//            prompt.append(JSONObject.toJSONString(toolResult.get("nl2sql"), true));
//            prompt.append("\n```\n\n");
//        }

        // Replace the single execute_sql check with dynamic collection
        Map<String, Object> allSqlResults = new HashMap<>();
        for (Map.Entry<String, Object> entry : toolResult.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith("execute_sql")) {
                allSqlResults.put(key, entry.getValue());
                prompt.append("### SQL执行结果 (").append(key).append(")\n");
                prompt.append("```json\n");
                prompt.append(JSONObject.toJSONString(entry.getValue(), true));
                prompt.append("\n```\n\n");
            }
        }
        if (toolResult.containsKey("echarts_render")) {
            prompt.append("### 图表配置 (echarts_render)\n");
            prompt.append("```json\n");
            prompt.append(JSONObject.toJSONString(toolResult.get("echarts_render"), true));
            prompt.append("\n```\n\n");
        }

        // 显示其他工具结果
        for (Map.Entry<String, Object> entry : toolResult.entrySet()) {
            String key = entry.getKey();
            if (!Arrays.asList("schema_info", "execute_sql", "echarts_render").contains(key)) {
                prompt.append("### ").append(key).append("\n");
                prompt.append("```json\n");
                prompt.append(JSONObject.toJSONString(entry.getValue(), true));
                prompt.append("\n```\n\n");
            }
        }

        prompt.append("## 报告要求\n");
        prompt.append("请生成一份完整的Markdown格式银行数据分析报告，包含：\n");
        prompt.append("1. 用户问题概述\n");
        prompt.append("2. 数据分析总体情况\n");
        prompt.append("3. 分角度详细分析（基于SQL查询结果、数据表格、ECharts图表配置）\n");
        prompt.append("4. 综合结论和建议\n\n");

        prompt.append("## 重要要求\n");
        prompt.append("- 严禁编造任何数据，所有分析必须基于提供的真实工具执行结果\n");
        prompt.append("- 如果有ECharts配置，请生成完整的ECharts HTML代码块\n");
        prompt.append("- 报告格式必须是标准的Markdown格式\n");
        prompt.append("- 重点关注银行业务风险指标和合规要求\n");
        prompt.append("- 确保数据的准确性和完整性\n");

        return prompt.toString();
    }

    /**
     * 增强报告内容 - 修复版本
     */
    private String enhanceReportWithCharts(String reportContent, Map<String, Object> toolResult) {
        StringBuilder enhancedReport = new StringBuilder(reportContent);

        // Process all execute_sql_* results
        Map<String, Object> allSqlResults = toolResult.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("execute_sql"))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (!allSqlResults.isEmpty()) {
            enhancedReport.append("\n\n## 数据可视化图表\n\n");

            int validChartCount = 0;
            for (Map.Entry<String, Object> sqlEntry : allSqlResults.entrySet()) {
                String datasetName = sqlEntry.getKey();
                Object sqlData = sqlEntry.getValue();

                // Generate chart for each dataset
                String chartHtml = generateDynamicChart(sqlData, datasetName);

                // 只有当图表HTML不为空时才添加
                if (!chartHtml.trim().isEmpty()) {
                    enhancedReport.append("### ").append(datasetName).append(" 图表\n\n");
                    enhancedReport.append(chartHtml);
                    validChartCount++;
                }

                // Generate data table
                String dataTable = generateDataTable(sqlData);
                if (!dataTable.trim().isEmpty()) {
                    enhancedReport.append("\n\n### ").append(datasetName).append(" 数据表格\n\n");
                    enhancedReport.append(dataTable);
                }
            }

            // 如果没有有效的图表，移除图表章节标题
            if (validChartCount == 0) {
                String content = enhancedReport.toString();
                content = content.replace("\n\n## 数据可视化图表\n\n", "");
                enhancedReport = new StringBuilder(content);
            }
        }

        return enhancedReport.toString();
    }
    /**
     * 生成动态图表 - 修复版本
     */
    private String generateDynamicChart(Object sqlData, String datasetName) {
        // Extract actual data from nested structure
        List<Map<String, Object>> dataList = extractSqlDataFromResult(sqlData);

        if (dataList.isEmpty()) {
            // 返回空字符串而不是错误信息，避免生成空白区域
            return "";
        }

        // Analyze data structure to determine chart type
        Map<String, Object> firstRow = dataList.get(0);
        List<String> numericColumns = new ArrayList<>();
        List<String> categoryColumns = new ArrayList<>();
        List<String> dateColumns = new ArrayList<>();

        for (Map.Entry<String, Object> column : firstRow.entrySet()) {
            String columnName = column.getKey();
            Object value = column.getValue();

            if (columnName.contains("date") || columnName.contains("time")) {
                dateColumns.add(columnName);
            } else if (value instanceof Number) {
                numericColumns.add(columnName);
            } else {
                categoryColumns.add(columnName);
            }
        }

        // 验证是否有足够的数据生成图表
        if (numericColumns.isEmpty() && categoryColumns.isEmpty()) {
            return "";
        }

        // Generate appropriate chart configuration
        Map<String, Object> chartConfig = generateChartConfig(dataList, numericColumns, categoryColumns, dateColumns, datasetName);

        // 验证图表配置是否有效
        if (chartConfig.isEmpty() || !isValidChartConfig(chartConfig)) {
            return "";
        }

        return generateChartHtml(chartConfig);
    }
    /**
     * 验证图表配置是否有效
     */
    private boolean isValidChartConfig(Map<String, Object> chartConfig) {
        if (chartConfig == null || chartConfig.isEmpty()) {
            return false;
        }

        // 检查是否有series数据
        Object series = chartConfig.get("series");
        if (series instanceof List) {
            List<?> seriesList = (List<?>) series;
            if (seriesList.isEmpty()) {
                return false;
            }

            // 检查每个series是否有数据
            for (Object seriesItem : seriesList) {
                if (seriesItem instanceof Map) {
                    Map<?, ?> seriesMap = (Map<?, ?>) seriesItem;
                    Object data = seriesMap.get("data");
                    if (data instanceof List && !((List<?>) data).isEmpty()) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private Map<String, Object> generateChartConfig(List<Map<String, Object>> dataList,
                                                    List<String> numericColumns,
                                                    List<String> categoryColumns,
                                                    List<String> dateColumns,
                                                    String datasetName) {
        Map<String, Object> option = new HashMap<>();

        // Basic chart structure
        Map<String, Object> title = new HashMap<>();
        title.put("text", datasetName.replace("execute_sql_", "数据集_") + " 分析图表");
        option.put("title", title);

        Map<String, Object> tooltip = new HashMap<>();
        tooltip.put("trigger", "axis");
        option.put("tooltip", tooltip);

        Map<String, Object> legend = new HashMap<>();
        option.put("legend", legend);

        // Determine X-axis (prefer date columns, then category columns)
        final String xAxisColumn; // Make it final
        if (!dateColumns.isEmpty()) {
            xAxisColumn = dateColumns.get(0);
        } else if (!categoryColumns.isEmpty()) {
            xAxisColumn = categoryColumns.get(0);
        } else {
            xAxisColumn = null;
        }

        if (xAxisColumn != null && !numericColumns.isEmpty()) {
            // Time series or category chart
            Map<String, Object> xAxis = new HashMap<>();
            xAxis.put("type", "category");

            List<Object> categories = dataList.stream()
                    .map(row -> row.get(xAxisColumn)) // Now xAxisColumn is effectively final
                    .collect(Collectors.toList());
            xAxis.put("data", categories);

            if (dateColumns.contains(xAxisColumn)) {
                Map<String, Object> axisLabel = new HashMap<>();
                axisLabel.put("rotate", 45);
                xAxis.put("axisLabel", axisLabel);
            }
            option.put("xAxis", xAxis);

            Map<String, Object> yAxis = new HashMap<>();
            yAxis.put("type", "value");
            option.put("yAxis", yAxis);

            // Create series for each numeric column
            List<Map<String, Object>> series = new ArrayList<>();
            for (String numericColumn : numericColumns) {
                Map<String, Object> serie = new HashMap<>();
                serie.put("name", numericColumn);
                serie.put("type", dateColumns.contains(xAxisColumn) ? "line" : "bar");

                List<Object> data = dataList.stream()
                        .map(row -> row.get(numericColumn))
                        .collect(Collectors.toList());
                serie.put("data", data);

                if ("line".equals(serie.get("type"))) {
                    serie.put("smooth", true);
                }

                series.add(serie);
            }
            option.put("series", series);

        } else if (!categoryColumns.isEmpty() && !numericColumns.isEmpty()) {
            // Pie chart for category data
            Map<String, Object> serie = new HashMap<>();
            serie.put("type", "pie");
            serie.put("radius", "50%");

            final String nameColumn = categoryColumns.get(0);
            final String valueColumn = numericColumns.get(0);

            List<Map<String, Object>> pieData = dataList.stream()
                    .map(row -> {
                        Map<String, Object> pieItem = new HashMap<>();
                        pieItem.put("name", row.get(nameColumn));
                        pieItem.put("value", row.get(valueColumn));
                        return pieItem;
                    })
                    .collect(Collectors.toList());

            serie.put("data", pieData);
            option.put("series", Arrays.asList(serie));
        }

        return option;
    }

    /**
     * 生成图表HTML代码
     */
    private String generateChartHtml(Map<String, Object> chartConfig) {
        StringBuilder chartHtml = new StringBuilder();

        chartHtml.append("```html\n");
        chartHtml.append("<!DOCTYPE html>\n");
        chartHtml.append("<html>\n");
        chartHtml.append("<head>\n");
        chartHtml.append("    <script src=\"http://**************:19000/echarts.min.js\"></script>\n");
        chartHtml.append("</head>\n");
        chartHtml.append("<body>\n");
        chartHtml.append("    <div id=\"chart\" style=\"width: 800px; height: 400px;\"></div>\n");
        chartHtml.append("    <script>\n");
        chartHtml.append("        var chart = echarts.init(document.getElementById('chart'));\n");
        chartHtml.append("        var option = ").append(JSONObject.toJSONString(chartConfig)).append(";\n");
        chartHtml.append("        chart.setOption(option);\n");
        chartHtml.append("    </script>\n");
        chartHtml.append("</body>\n");
        chartHtml.append("</html>\n");
        chartHtml.append("```\n");

        return chartHtml.toString();
    }

    /**
     * 生成数据表格
     */
    private String generateDataTable(Object sqlResults) {
        StringBuilder table = new StringBuilder();

        try {
            List<Map<String, Object>> dataList = extractSqlDataFromResult(sqlResults);

            if (!dataList.isEmpty()) {
                Map<String, Object> firstRow = dataList.get(0);

                // Generate table header
                table.append("| ");
                for (String key : firstRow.keySet()) {
                    table.append(key).append(" | ");
                }
                table.append("\n");

                // Generate separator line
                table.append("| ");
                for (int i = 0; i < firstRow.size(); i++) {
                    table.append("--- | ");
                }
                table.append("\n");

                // Generate data rows
                for (Map<String, Object> row : dataList) {
                    table.append("| ");
                    for (Object value : row.values()) {
                        String displayValue = "";
                        if (value != null) {
                            if (value instanceof Number) {
                                // Format numbers for better readability
                                displayValue = String.format("%,.2f", ((Number) value).doubleValue());
                            } else {
                                displayValue = value.toString();
                            }
                        }
                        table.append(displayValue).append(" | ");
                    }
                    table.append("\n");
                }
            }
        } catch (Exception e) {
            log.error("{} 生成数据表格失败", agentContext.getRequestId(), e);
            table.append("数据表格生成失败\n");
        }

        return table.toString();
    }
    /**
     * 生成多种格式的文件
     */
    private List<String> generateMultipleFormatFiles(String reportContent, String query, String format) {
        List<String> generatedFiles = new ArrayList<>();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

        try {
            // 生成Markdown报告
            String markdownFileName = "银行数据分析报告_" + timestamp + ".md";
            saveReportFile(markdownFileName, reportContent, "Markdown格式的银行数据分析报告");
            generatedFiles.add(markdownFileName);

            // 生成HTML报告（用于网页展示）
            String htmlContent = convertMarkdownToHtml(reportContent);
            String htmlFileName = "银行数据分析报告_" + timestamp + ".html";
            saveReportFile(htmlFileName, htmlContent, "HTML格式的银行数据分析报告");
            generatedFiles.add(htmlFileName);

            // 生成Excel数据文件（如果有数据表格）
            Map<String, Object> toolResult = agentContext.getAllToolResults();
            if (toolResult.containsKey("execute_sql")) {
                String excelFileName = generateExcelReport(toolResult, timestamp);
                if (excelFileName != null) {
                    generatedFiles.add(excelFileName);
                    log.info("{} Excel数据文件生成成功: {}", agentContext.getRequestId(), excelFileName);
                }
            }

        } catch (Exception e) {
            log.error("{} 生成多格式文件失败", agentContext.getRequestId(), e);
        }

        return generatedFiles;
    }

    /**
     * 保存报告文件
     */
    private void saveReportFile(String fileName, String content, String description) {
        try {
            FileTool fileTool = new FileTool();
            fileTool.setAgentContext(agentContext);

            Map<String, Object> fileParams = new HashMap<>();
            fileParams.put("command", "upload");
            fileParams.put("filename", fileName);
            fileParams.put("description", description);
            fileParams.put("content", content);

            String uploadResult = (String) fileTool.execute(fileParams);
            if (uploadResult != null) {
                log.info("{} 报告文件保存成功: {}", agentContext.getRequestId(), fileName);
            }
        } catch (Exception e) {
            log.error("{} 保存报告文件失败: {}", agentContext.getRequestId(), fileName, e);
        }
    }

    /**
     * 将Markdown转换为规范的HTML页面
     */
    private String convertMarkdownToHtml(String markdownContent) {
        StringBuilder htmlBuilder = new StringBuilder();

        // HTML文档头部
        htmlBuilder.append("<!DOCTYPE html>\n");
        htmlBuilder.append("<html lang=\"zh-CN\">\n");
        htmlBuilder.append("<head>\n");
        htmlBuilder.append("    <meta charset=\"UTF-8\">\n");
        htmlBuilder.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        htmlBuilder.append("    <title>银行数据分析报告</title>\n");
        htmlBuilder.append("    <script src=\"http://**************:19000/echarts.min.js\"></script>\n");
        htmlBuilder.append("    <style>\n");
        htmlBuilder.append(generateCSS());
        htmlBuilder.append("    </style>\n");
        htmlBuilder.append("</head>\n");
        htmlBuilder.append("<body>\n");
        htmlBuilder.append("    <div class=\"container\">\n");

        // 处理内容
        String processedContent = processMarkdownContent(markdownContent);
        htmlBuilder.append(processedContent);

        // 页面底部
        htmlBuilder.append("    </div>\n");
        htmlBuilder.append("    <footer>\n");
        htmlBuilder.append("        <p>Created by goData | 页面内容均由 AI 生成，仅供参考</p>\n");
        htmlBuilder.append("    </footer>\n");
        htmlBuilder.append("    <script>\n");
        htmlBuilder.append(generateJavaScript());
        htmlBuilder.append("    </script>\n");
        htmlBuilder.append("</body>\n");
        htmlBuilder.append("</html>");

        return htmlBuilder.toString();
    }

    /**
     * 生成CSS样式
     */
    private String generateCSS() {
        return """  
        body {  
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;  
            line-height: 1.6;  
            color: #333;  
            margin: 0;  
            padding: 0;  
            background-color: #f5f7fa;  
        }  
          
        .container {  
            max-width: 1200px;  
            margin: 0 auto;  
            padding: 20px;  
            background-color: white;  
            box-shadow: 0 0 10px rgba(0,0,0,0.1);  
            border-radius: 8px;  
            margin-top: 20px;  
            margin-bottom: 20px;  
        }  
          
        h1 {  
            color: #2c3e50;  
            border-bottom: 3px solid #3498db;  
            padding-bottom: 10px;  
            margin-bottom: 30px;  
            font-size: 2.2em;  
        }  
          
        h2 {  
            color: #34495e;  
            border-left: 4px solid #3498db;  
            padding-left: 15px;  
            margin-top: 30px;  
            margin-bottom: 20px;  
            font-size: 1.5em;  
        }  
          
        h3 {  
            color: #2c3e50;  
            margin-top: 25px;  
            margin-bottom: 15px;  
            font-size: 1.2em;  
        }  
          
        table {  
            width: 100%;  
            border-collapse: collapse;  
            margin: 20px 0;  
            background-color: white;  
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);  
        }  
          
        th, td {  
            border: 1px solid #ddd;  
            padding: 12px;  
            text-align: left;  
        }  
          
        th {  
            background-color: #3498db;  
            color: white;  
            font-weight: bold;  
        }  
          
        tr:nth-child(even) {  
            background-color: #f9f9f9;  
        }  
          
        tr:hover {  
            background-color: #f5f5f5;  
        }  
          
        .chart-container {  
            margin: 30px 0;  
            padding: 20px;  
            background-color: #fafafa;  
            border-radius: 8px;  
            border: 1px solid #e0e0e0;  
        }  
          
        .chart-title {  
            font-size: 1.3em;  
            font-weight: bold;  
            margin-bottom: 15px;  
            color: #2c3e50;  
            text-align: center;  
        }  
          
        .highlight {  
            background-color: #fff3cd;  
            border: 1px solid #ffeaa7;  
            padding: 15px;  
            border-radius: 5px;  
            margin: 15px 0;  
        }  
          
        .risk-warning {  
            background-color: #f8d7da;  
            border: 1px solid #f5c6cb;  
            color: #721c24;  
            padding: 15px;  
            border-radius: 5px;  
            margin: 15px 0;  
        }  
          
        .business-insight {  
            background-color: #d1ecf1;  
            border: 1px solid #bee5eb;  
            color: #0c5460;  
            padding: 15px;  
            border-radius: 5px;  
            margin: 15px 0;  
        }  
          
        ul, ol {  
            padding-left: 25px;  
        }  
          
        li {  
            margin-bottom: 8px;  
        }  
          
        strong {  
            color: #2c3e50;  
        }  
          
        footer {  
            text-align: center;  
            padding: 20px;  
            background-color: #34495e;  
            color: white;  
            margin-top: 40px;  
        }  
          
        .data-summary {  
            display: grid;  
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));  
            gap: 20px;  
            margin: 20px 0;  
        }  
          
        .summary-card {  
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);  
            color: white;  
            padding: 20px;  
            border-radius: 10px;  
            text-align: center;  
        }  
          
        .summary-card h4 {  
            margin: 0 0 10px 0;  
            font-size: 1.1em;  
        }  
          
        .summary-card .value {  
            font-size: 2em;  
            font-weight: bold;  
            margin: 10px 0;  
        }  
        """;
    }

    /**
     * 处理Markdown内容转换为HTML - 支持echarts配置块
     */
    private String processMarkdownContent(String content) {
        StringBuilder result = new StringBuilder();
        String[] lines = content.split("\n");
        boolean inTable = false;
        boolean inChart = false;
        boolean inEchartsConfig = false; // 新增：处理echarts配置块
        StringBuilder chartContent = new StringBuilder();
        StringBuilder echartsConfigContent = new StringBuilder(); // 新增：存储echarts配置
        int chartCounter = 0;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 处理echarts配置代码块 - 新增
            if (line.startsWith("```echarts")) {
                inEchartsConfig = true;
                echartsConfigContent.setLength(0);
                continue;
            }

            if (line.equals("```") && inEchartsConfig) {
                inEchartsConfig = false;

                // 将echarts配置转换为图表
                if (echartsConfigContent.length() > 0) {
                    String chartId = "echarts_config_" + (++chartCounter) + "_" + System.currentTimeMillis();
                    String chartHtml = generateEChartsFromConfig(echartsConfigContent.toString(), chartId);

                    if (!chartHtml.trim().isEmpty()) {
                        result.append("<div class=\"chart-container\">\n");
                        result.append(chartHtml);
                        result.append("</div>\n");
                    }
                }

                echartsConfigContent.setLength(0);
                continue;
            }

            if (inEchartsConfig) {
                echartsConfigContent.append(line).append("\n");
                continue;
            }

            // 处理HTML图表代码块 - 保持原有逻辑
            if (line.startsWith("```html")) {
                inChart = true;
                chartContent.setLength(0);
                continue;
            }

            if (line.equals("```") && inChart) {
                inChart = false;

                if (chartContent.length() > 0) {
                    String chartId = "chart_" + (++chartCounter) + "_" + System.currentTimeMillis();
                    String chartHtml = generateEChartsDiv(chartContent.toString(), chartId);

                    if (!chartHtml.trim().isEmpty()) {
                        result.append("<div class=\"chart-container\">\n");
                        result.append(chartHtml);
                        result.append("</div>\n");
                    }
                }

                chartContent.setLength(0);
                continue;
            }

            if (inChart) {
                chartContent.append(line).append("\n");
                continue;
            }

            // 处理标题
            if (line.startsWith("# ")) {
                result.append("<h1>").append(line.substring(2)).append("</h1>\n");
            } else if (line.startsWith("## ")) {
                result.append("<h2>").append(line.substring(3)).append("</h2>\n");
            } else if (line.startsWith("### ")) {
                result.append("<h3>").append(line.substring(4)).append("</h3>\n");
            } else if (line.startsWith("#### ")) {
                result.append("<h4>").append(line.substring(5)).append("</h4>\n");
            }
            // 处理表格
            else if (line.startsWith("|") && line.endsWith("|")) {
                if (!inTable) {
                    result.append("<table>\n");
                    inTable = true;
                    // 表头
                    result.append("<thead><tr>");
                    String[] headers = line.split("\\|");
                    for (int j = 1; j < headers.length - 1; j++) {
                        result.append("<th>").append(headers[j].trim()).append("</th>");
                    }
                    result.append("</tr></thead>\n<tbody>\n");
                    // 跳过分隔线
                    i++;
                } else {
                    // 表格数据行
                    result.append("<tr>");
                    String[] cells = line.split("\\|");
                    for (int j = 1; j < cells.length - 1; j++) {
                        result.append("<td>").append(cells[j].trim()).append("</td>");
                    }
                    result.append("</tr>\n");
                }
            } else {
                if (inTable) {
                    result.append("</tbody></table>\n");
                    inTable = false;
                }

                // 处理普通文本
                if (!line.isEmpty()) {
                    // 处理粗体
                    line = line.replaceAll("\\*\\*(.*?)\\*\\*", "<strong>$1</strong>");
                    // 处理列表
                    if (line.startsWith("- ")) {
                        result.append("<ul><li>").append(line.substring(2)).append("</li></ul>\n");
                    } else if (line.matches("^\\d+\\.\\s.*")) {
                        result.append("<ol><li>").append(line.replaceFirst("^\\d+\\.\\s", "")).append("</li></ol>\n");
                    } else {
                        result.append("<p>").append(line).append("</p>\n");
                    }
                }
            }
        }

        if (inTable) {
            result.append("</tbody></table>\n");
        }

        return result.toString();
    }
    /**
     * 从echarts配置生成图表HTML - 新增方法
     */
    private String generateEChartsFromConfig(String configContent, String chartId) {
        StringBuilder chartHtml = new StringBuilder();

        try {
            // 验证配置内容是否为有效的JSON
            String cleanConfig = configContent.trim();
            if (cleanConfig.isEmpty()) {
                return "";
            }

            // 尝试解析JSON以验证格式
            JSONObject.parseObject(cleanConfig);

            // 使用更安全的ID生成方式
            String safeChartId = chartId + "_" + Math.abs(cleanConfig.hashCode());

            chartHtml.append("<div id=\"").append(safeChartId).append("\" style=\"width: 100%; height: 400px; margin: 20px 0;\"></div>\n");
            chartHtml.append("<script>\n");
            chartHtml.append("(function() {\n");
            chartHtml.append("    var chartDom = document.getElementById('").append(safeChartId).append("');\n");
            chartHtml.append("    if (!chartDom) {\n");
            chartHtml.append("        console.error('Chart container not found: ").append(safeChartId).append("');\n");
            chartHtml.append("        return;\n");
            chartHtml.append("    }\n");
            chartHtml.append("    \n");
            chartHtml.append("    // 销毁可能存在的旧图表实例\n");
            chartHtml.append("    if (chartDom._echarts_instance_) {\n");
            chartHtml.append("        chartDom._echarts_instance_.dispose();\n");
            chartHtml.append("    }\n");
            chartHtml.append("    \n");
            chartHtml.append("    var myChart = echarts.init(chartDom);\n");
            chartHtml.append("    var option = ").append(cleanConfig).append(";\n");
            chartHtml.append("    myChart.setOption(option);\n");
            chartHtml.append("    \n");
            chartHtml.append("    // 响应式调整\n");
            chartHtml.append("    window.addEventListener('resize', function() {\n");
            chartHtml.append("        if (myChart && !myChart.isDisposed()) {\n");
            chartHtml.append("            myChart.resize();\n");
            chartHtml.append("        }\n");
            chartHtml.append("    });\n");
            chartHtml.append("})();\n");
            chartHtml.append("</script>\n");

        } catch (Exception e) {
            log.error("{} 解析echarts配置失败: {}", agentContext.getRequestId(), e.getMessage());
            return "";
        }

        return chartHtml.toString();
    }
    /**
     * 生成ECharts图表HTML - 修复版本
     */
    private String generateEChartsDiv(String chartContent, String chartId) {
        StringBuilder chartHtml = new StringBuilder();

        // 验证图表内容是否有效
        String optionConfig = extractEChartsOption(chartContent);
        if (optionConfig.equals("{ title: { text: '数据图表' }, tooltip: {}, xAxis: { type: 'category', data: [] }, yAxis: { type: 'value' }, series: [] }")) {
            // 如果是默认配置（无效数据），不生成图表
            return "";
        }

        // 提取图表配置
        String title = extractChartTitle(chartContent);
        if (title != null) {
            chartHtml.append("<div class=\"chart-title\">").append(title).append("</div>\n");
        }

        // 使用更安全的ID生成方式，避免重复
        String safeChartId = chartId + "_" + Math.abs(chartContent.hashCode());

        chartHtml.append("<div id=\"").append(safeChartId).append("\" style=\"width: 100%; height: 400px; margin: 20px 0;\"></div>\n");
        chartHtml.append("<script>\n");
        chartHtml.append("(function() {\n");
        chartHtml.append("    // 确保DOM元素存在且未被占用\n");
        chartHtml.append("    var chartDom = document.getElementById('").append(safeChartId).append("');\n");
        chartHtml.append("    if (!chartDom) {\n");
        chartHtml.append("        console.error('Chart container not found: ").append(safeChartId).append("');\n");
        chartHtml.append("        return;\n");
        chartHtml.append("    }\n");
        chartHtml.append("    \n");
        chartHtml.append("    // 销毁可能存在的旧图表实例\n");
        chartHtml.append("    if (chartDom._echarts_instance_) {\n");
        chartHtml.append("        chartDom._echarts_instance_.dispose();\n");
        chartHtml.append("    }\n");
        chartHtml.append("    \n");
        chartHtml.append("    var myChart = echarts.init(chartDom);\n");

        chartHtml.append("    var option = ").append(optionConfig).append(";\n");

        chartHtml.append("    myChart.setOption(option);\n");
        chartHtml.append("    \n");
        chartHtml.append("    // 响应式调整\n");
        chartHtml.append("    window.addEventListener('resize', function() {\n");
        chartHtml.append("        if (myChart && !myChart.isDisposed()) {\n");
        chartHtml.append("            myChart.resize();\n");
        chartHtml.append("        }\n");
        chartHtml.append("    });\n");
        chartHtml.append("})();\n");
        chartHtml.append("</script>\n");

        return chartHtml.toString();
    }

    /**
     * 提取图表标题
     */
    private String extractChartTitle(String content) {
        // 从HTML内容中提取title
        if (content.contains("<title>")) {
            int start = content.indexOf("<title>") + 7;
            int end = content.indexOf("</title>");
            if (end > start) {
                return content.substring(start, end);
            }
        }
        return null;
    }

    /**
     * 提取ECharts配置
     */
    private String extractEChartsOption(String content) {
        // 提取option配置对象
        int optionStart = content.indexOf("option = {");
        if (optionStart != -1) {
            int braceCount = 0;
            int start = optionStart + 9; // "option = ".length()
            int end = start;

            for (int i = start; i < content.length(); i++) {
                char c = content.charAt(i);
                if (c == '{') braceCount++;
                else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        end = i + 1;
                        break;
                    }
                }
            }

            if (end > start) {
                return content.substring(start, end);
            }
        }

        // 默认配置
        return "{ title: { text: '数据图表' }, tooltip: {}, xAxis: { type: 'category', data: [] }, yAxis: { type: 'value' }, series: [] }";
    }

    /**
     * 生成JavaScript代码
     */
    private String generateJavaScript() {
        return """  
        // 页面加载完成后的初始化  
        document.addEventListener('DOMContentLoaded', function() {  
            // 添加表格交互效果  
            const tables = document.querySelectorAll('table');  
            tables.forEach(table => {  
                table.addEventListener('mouseover', function(e) {  
                    if (e.target.tagName === 'TD') {  
                        e.target.style.backgroundColor = '#e3f2fd';  
                    }  
                });  
                table.addEventListener('mouseout', function(e) {  
                    if (e.target.tagName === 'TD') {  
                        e.target.style.backgroundColor = '';  
                    }  
                });  
            });  
              
            // 平滑滚动效果  
            const links = document.querySelectorAll('a[href^="#"]');  
            links.forEach(link => {  
                link.addEventListener('click', function(e) {  
                    e.preventDefault();  
                    const target = document.querySelector(this.getAttribute('href'));  
                    if (target) {  
                        target.scrollIntoView({  
                            behavior: 'smooth',  
                            block: 'start'  
                        });  
                    }  
                });  
            });  
              
            // 响应式图表调整  
            window.addEventListener('resize', function() {  
                // ECharts图表会自动调整，这里可以添加其他响应式逻辑  
                console.log('页面大小已调整');  
            });  
        });  
          
        // 添加页面加载动画  
        window.addEventListener('load', function() {  
            document.body.style.opacity = '0';  
            document.body.style.transition = 'opacity 0.5s ease-in-out';  
            setTimeout(function() {  
                document.body.style.opacity = '1';  
            }, 100);  
        });  
        """;
    }
    /**
     * 生成Excel报告
     */
    private String generateExcelReport(Map<String, Object> toolResult, String timestamp) {
        try {
            String excelFileName = "银行数据分析数据_" + timestamp + ".csv";
            StringBuilder csvContent = new StringBuilder();

            Object sqlResults = toolResult.get("execute_sql");
            if (sqlResults instanceof List) {
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) sqlResults;
                if (!dataList.isEmpty()) {
                    Map<String, Object> firstRow = dataList.get(0);

                    // CSV表头
                    csvContent.append(String.join(",", firstRow.keySet())).append("\n");
                    // CSV数据行
                    for (Map<String, Object> row : dataList) {
                        List<String> values = new ArrayList<>();
                        for (Object value : row.values()) {
                            values.add(value != null ? value.toString() : "");
                        }
                        csvContent.append(String.join(",", values)).append("\n");
                    }

                    // 保存CSV文件
                    saveReportFile(excelFileName, csvContent.toString(), "银行数据分析原始数据CSV文件");
                    return excelFileName;
                }
            }
        } catch (Exception e) {
            log.error("{} 生成Excel报告失败", agentContext.getRequestId(), e);
        }
        return null;
    }

    /**
     * 发送任务完成结果
     */
    private void sendTaskCompletionResult(List<String> generatedFiles) {
        try {
            // 构建任务总结结果并发送
            Map<String, Object> taskResult = new HashMap<>();
            taskResult.put("taskSummary", "银行数据分析报告已生成完成，包含 " + generatedFiles.size() + " 个文件，已保存并可供下载");

            // 获取最新添加的文件列表
            List<File> recentFiles = agentContext.getProductFiles();
            if (!recentFiles.isEmpty()) {
                // 获取最近生成的文件（根据生成的文件数量）
                int startIndex = Math.max(0, recentFiles.size() - generatedFiles.size());
                List<File> latestFiles = recentFiles.subList(startIndex, recentFiles.size());
                taskResult.put("fileList", latestFiles);
            }

            // 发送最终结果，触发任务完成
            agentContext.getPrinter().send("result", taskResult);

            log.info("{} 银行数据分析报告任务完成结果已发送", agentContext.getRequestId());

        } catch (Exception e) {
            log.error("{} 发送任务完成结果失败", agentContext.getRequestId(), e);
        }
    }

    /**
     * 生成降级报告（当LLM调用失败时使用）
     */
    private String generateFallbackReport(String query, Map<String, Object> toolResult, String analysis) {
        StringBuilder report = new StringBuilder();

        // 基础报告结构
        report.append("# 银行数据分析报告\n\n");
        report.append("**生成时间**: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");

        report.append("## 1. 用户问题概述\n\n");
        report.append("**原始问题**: ").append(query).append("\n\n");

        report.append("## 2. 数据分析总体情况\n\n");
        report.append(analysis != null ? analysis : "数据分析总结信息不可用").append("\n\n");

        report.append("## 3. 工具执行结果概览\n\n");
        if (toolResult != null && !toolResult.isEmpty()) {
            for (Map.Entry<String, Object> entry : toolResult.entrySet()) {
                report.append("### ").append(entry.getKey()).append("\n");
                report.append("```json\n");
                report.append(JSONObject.toJSONString(entry.getValue(), true));
                report.append("\n```\n\n");
            }
        } else {
            report.append("暂无工具执行结果数据\n\n");
        }

        report.append("## 4. 说明\n\n");
        report.append("由于LLM调用失败，此报告为基础格式。请检查系统配置后重新生成完整报告。\n\n");

        return report.toString();
    }

    /**
     * 获取LLM实例
     */
    private LLM getLlm() {
        if (this.llm == null) {
            ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();
            GenieConfig genieConfig = applicationContext.getBean(GenieConfig.class);
            this.llm = new LLM(genieConfig.getReactModelName(), "");
        }
        return this.llm;
    }

    private List<Map<String, Object>> extractSqlDataFromResult(Object sqlResult) {
        try {
            if (sqlResult instanceof String) {
                JSONObject outerJson = JSONObject.parseObject((String) sqlResult);
                if (outerJson.getInteger("code") == 200) {
                    JSONObject data = outerJson.getJSONObject("data");
                    if (data != null && data.containsKey("content")) {
                        JSONArray content = data.getJSONArray("content");
                        if (!content.isEmpty()) {
                            JSONObject firstContent = content.getJSONObject(0);
                            if ("text".equals(firstContent.getString("type"))) {
                                String textContent = firstContent.getString("text");
                                JSONObject innerJson = JSONObject.parseObject(textContent);
                                if (innerJson.getInteger("code") == 200) {
                                    JSONArray dataArray = innerJson.getJSONArray("data");
                                    // Fix: Use the correct Fastjson method
                                    List<Map<String, Object>> result = new ArrayList<>();
                                    for (int i = 0; i < dataArray.size(); i++) {
                                        JSONObject item = dataArray.getJSONObject(i);
                                        result.add(item.getInnerMap());
                                    }
                                    return result;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("{} 解析SQL结果数据失败", agentContext.getRequestId(), e);
        }
        return new ArrayList<>();
    }
}