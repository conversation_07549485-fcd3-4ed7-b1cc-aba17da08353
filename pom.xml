<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.suite.chatdatabi</groupId>
    <artifactId>hchatdata</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <modules>
        <module>auth</module>
        <module>chat</module>
        <module>common</module>
        <module>launchers</module>
        <module>headless</module>
        <module>graph</module>
    </modules>

    <name>hchatdata</name>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.9</version>
        <relativePath/>
    </parent>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <java.source.version>21</java.source.version>
        <java.target.version>21</java.target.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <file.encoding>UTF-8</file.encoding>
        <jsqlparser.version>4.9</jsqlparser.version>
        <pagehelper.version>6.1.0</pagehelper.version>
        <pagehelper.spring.version>2.1.0</pagehelper.spring.version>
        <mybatis.version>3.5.19</mybatis.version>
        <guava.version>32.0.0-jre</guava.version>
        <hanlp.version>portable-1.8.4</hanlp.version>
        <hadoop.version>2.7.2</hadoop.version>
        <commons.lang.version>2.6</commons.lang.version>
        <commons.lang3.version>3.7</commons.lang3.version>
        <org.testng.version>6.13.1</org.testng.version>
        <yaml.utils.version>2.14.1</yaml.utils.version>
        <jjwt.version>0.12.6</jjwt.version>
        <alibaba.druid.version>1.2.24</alibaba.druid.version>
        <mysql.connector.java.version>9.2.0</mysql.connector.java.version>
        <kyuubi.version>1.10.1</kyuubi.version>
        <presto.version>0.291</presto.version>
        <trino.version>471</trino.version>
        <mybatis.plus.version>3.5.10.1</mybatis.plus.version>
        <httpclient5.version>5.4.2</httpclient5.version>
        <!--        <httpcore.version>4.4.16</httpcore.version>-->
        <httpcore5.version>5.3.3</httpcore5.version>
        <clickhouse.jdbc.version>0.4.6</clickhouse.jdbc.version>
        <fastjson.version>2.0.56</fastjson.version>
        <dozer.verson>7.0.0</dozer.verson>
        <!--        <httpmime.version>4.5.6</httpmime.version>-->
        <transmittable.version>2.14.5</transmittable.version>
        <commons.compress.version>1.27.1</commons.compress.version>
        <jetty.util.version>6.1.26</jetty.util.version>
        <!--<spring.version>2.7.2</spring.version>-->
        <jsonpath.version>2.8.0</jsonpath.version>
        <calcite.version>1.38.0</calcite.version>
        <calcite.avatica.version>1.26.0</calcite.avatica.version>
        <xk.time.version>3.2.4</xk.time.version>
        <mockito-inline.version>4.5.1</mockito-inline.version>
        <easyexcel.version>2.2.11</easyexcel.version>
        <poi.version>3.17</poi.version>
        <langchain4j.version>0.36.2</langchain4j.version>
        <langchain4j.embedding.version>0.36.2</langchain4j.embedding.version>
        <!--        <postgresql.version>42.7.1</postgresql.version>-->
        <st.version>4.0.8</st.version>
        <duckdb_jdbc.version>0.10.0</duckdb_jdbc.version>
        <flight-sql.version>15.0.2</flight-sql.version>
        <arrow-jdbc.version>15.0.2</arrow-jdbc.version>
        <flight-sql-jdbc-driver.version>15.0.2</flight-sql-jdbc-driver.version>
        <gson.version>2.12.1</gson.version>
        <spotless.version>2.27.1</spotless.version>
        <spotless.skip>false</spotless.skip>
        <stax2.version>4.2.2</stax2.version>
        <aws-java-sdk.version>1.12.780</aws-java-sdk.version>
        <jgrapht.version>1.5.2</jgrapht.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <spring-ai-alibaba.version>*******</spring-ai-alibaba.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--pagehelper-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!--guava-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!--langchain4j-->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-parent</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-core</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-local-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-chroma</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-embeddings</artifactId>
                <version>${langchain4j.embedding.version}</version>
                <!--由于mac intne不支持0.30.0包修改-->
                <exclusions>
                    <exclusion>
                        <groupId>ai.djl</groupId>
                        <artifactId>api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ai.djl.huggingface</groupId>
                        <artifactId>tokenizers</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-embeddings-bge-small-zh</artifactId>
                <version>${langchain4j.embedding.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-azure-open-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-embeddings-all-minilm-l6-v2-q</artifactId>
                <version>${langchain4j.embedding.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-qianfan</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-zhipu-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-dashscope</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-milvus</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-opensearch</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-pgvector</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-chatglm</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-ollama</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId>
                <version>${stax2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kyuubi</groupId>
                <artifactId>kyuubi-hive-jdbc</artifactId>
                <version>${kyuubi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facebook.presto</groupId>
                <artifactId>presto-jdbc</artifactId>
                <version>${presto.version}</version>
            </dependency>
            <dependency>
                <groupId>io.trino</groupId>
                <artifactId>trino-jdbc</artifactId>
                <version>${trino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk</artifactId>
                <version>${aws-java-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jgrapht</groupId>
                <artifactId>jgrapht-core</artifactId>
                <version>${jgrapht.version}</version>
            </dependency>
            <!--由于mac intne不支持0.30.0包修改-->
            <dependency>
                <groupId>ai.djl</groupId>
                <artifactId>api</artifactId>
                <version>0.29.0</version>
                <exclusions>
                    <exclusion>
                        <!-- due to version conflict between dev.langchain4j:langchain4j-core and ai.djl:api -->
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>ai.djl.huggingface</groupId>
                <artifactId>tokenizers</artifactId>
                <version>0.29.0</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>aliyun-snapshots</id>
            <url>https://maven.aliyun.com/repository/snapshots</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>${java.source.version}</source>
                    <target>${java.target.version}</target>
                    <encoding>${file.encoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <propertiesEncoding>${project.build.sourceEncoding}</propertiesEncoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless.version}</version>
                <configuration>
                    <skip>${spotless.skip}</skip>
                    <java>
                        <eclipse>
                            <file>java-formatter.xml</file>
                        </eclipse>
                        <importOrder>
                            <order>javax,java,scala,\#</order>
                        </importOrder>
                    </java>
                </configuration>
                <executions>
                    <execution>
                        <id>spotless-apply-auto</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                        <configuration>
                            <skip>${spotless.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.6.1.1688</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.5.2</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.junit.jupiter</groupId>
                            <artifactId>junit-jupiter</artifactId>
                            <version>5.11.4</version>
                        </dependency>
                        <dependency>
                            <groupId>org.junit.platform</groupId>
                            <artifactId>junit-platform-launcher</artifactId>
                            <version>1.11.4</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <systemPropertyVariables>
                            <net.bytebuddy.experimental>true</net.bytebuddy.experimental>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
