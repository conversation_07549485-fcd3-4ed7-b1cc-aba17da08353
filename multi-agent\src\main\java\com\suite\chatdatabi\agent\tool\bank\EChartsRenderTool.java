package com.suite.chatdatabi.agent.tool.bank;

import com.suite.chatdatabi.agent.tool.BaseTool;
import com.suite.chatdatabi.agent.agent.AgentContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Data
public class EChartsRenderTool implements BaseTool {
    private AgentContext agentContext;

    @Override
    public String getName() {
        return "echarts_render";
    }

    @Override
    public String getDescription() {
        return "基于真实数据库查询结果生成ECharts图表配置的工具";
    }

    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        Map<String, Object> dataParam = new HashMap<>();
        dataParam.put("type", "array");
        dataParam.put("description", "真实的数据库查询结果数据");
        properties.put("data", dataParam);

        Map<String, Object> chartTypeParam = new HashMap<>();
        chartTypeParam.put("type", "string");
        chartTypeParam.put("description", "图表类型：bar, line, pie, scatter等");
        properties.put("chartType", chartTypeParam);

        Map<String, Object> titleParam = new HashMap<>();
        titleParam.put("type", "string");
        titleParam.put("description", "图表标题");
        properties.put("title", titleParam);

        params.put("properties", properties);
        params.put("required", Arrays.asList("data", "chartType", "title"));

        return params;
    }
    @Override
    public Object execute(Object input) {
        try {
            Map<String, Object> params = (Map<String, Object>) input;
            List<Map<String, Object>> data = (List<Map<String, Object>>) params.get("data");
            String chartType = (String) params.get("chartType");
            String title = (String) params.get("title");

            return generateEChartsConfig(data, chartType, title);
        } catch (Exception e) {
            log.error("{} EChartsConfigTool error", agentContext.getRequestId(), e);
            return "图表配置生成失败";
        }
    }

    private String generateEChartsConfig(List<Map<String, Object>> data, String chartType, String title) {
        StringBuilder config = new StringBuilder();
        String chartId = "chart_" + System.currentTimeMillis();

        // 生成HTML代码块，包含ECharts图表
        config.append("```html\n");
        config.append("<div id=\"").append(chartId).append("\" style=\"width: 100%; height: 400px;\"></div>\n");
        config.append("<script src=\"http://**************:19000/echarts.min.js\"></script>\n");
        config.append("<script>\n");
        config.append("var myChart = echarts.init(document.getElementById('").append(chartId).append("'));\n");

        // 根据图表类型生成配置
        switch (chartType.toLowerCase()) {
            case "bar":
                config.append(generateBarChartConfig(data, title));
                break;
            case "line":
                config.append(generateLineChartConfig(data, title));
                break;
            case "pie":
                config.append(generatePieChartConfig(data, title));
                break;
            default:
                config.append(generateBarChartConfig(data, title));
        }

        config.append("myChart.setOption(option);\n");
        config.append("window.addEventListener('resize', function() { myChart.resize(); });\n");
        config.append("</script>\n");
        config.append("```\n");

        return config.toString();
    }
    private String generateBarChartConfig(List<Map<String, Object>> data, String title) {
        StringBuilder config = new StringBuilder();
        config.append("var option = {\n");
        config.append("  title: { text: '").append(title).append("' },\n");
        config.append("  tooltip: { trigger: 'axis' },\n");
        config.append("  xAxis: {\n");
        config.append("    type: 'category',\n");
        config.append("    data: [");

        // 提取X轴数据
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> item = data.get(i);
            String xValue = item.keySet().iterator().next();
            config.append("'").append(item.get(xValue)).append("'");
            if (i < data.size() - 1) config.append(", ");
        }

        config.append("]\n  },\n");
        config.append("  yAxis: { type: 'value' },\n");
        config.append("  series: [{\n");
        config.append("    type: 'bar',\n");
        config.append("    data: [");

        // 提取Y轴数据
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> item = data.get(i);
            Object[] values = item.values().toArray();
            if (values.length > 1) {
                config.append(values[1]);
            } else {
                config.append("0");
            }
            if (i < data.size() - 1) config.append(", ");
        }

        config.append("]\n  }]\n};\n");
        return config.toString();
    }
    private String generateLineChartConfig(List<Map<String, Object>> data, String title) {
        // 类似柱状图，但type改为'line'
        return generateBarChartConfig(data, title).replace("type: 'bar'", "type: 'line'");
    }
    private String generatePieChartConfig(List<Map<String, Object>> data, String title) {
        StringBuilder config = new StringBuilder();
        config.append("var option = {\n");
        config.append("  title: { text: '").append(title).append("' },\n");
        config.append("  tooltip: { trigger: 'item' },\n");
        config.append("  series: [{\n");
        config.append("    type: 'pie',\n");
        config.append("    radius: '50%',\n");
        config.append("    data: [");

        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> item = data.get(i);
            Object[] keys = item.keySet().toArray();
            Object[] values = item.values().toArray();

            config.append("{ name: '").append(values[0]).append("', value: ").append(values.length > 1 ? values[1] : "0").append(" }");
            if (i < data.size() - 1) config.append(", ");
        }

        config.append("]\n  }]\n};\n");
        return config.toString();
    }
}