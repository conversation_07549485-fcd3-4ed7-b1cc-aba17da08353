package com.suite.chatdatabi.chat.api.pojo.response;

import com.suite.chatdatabi.headless.api.pojo.SemanticParseInfo;
import com.suite.chatdatabi.headless.api.pojo.response.ParseTimeCostResp;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class QueryResp {

    private Long questionId;
    private Date createTime;
    private Long chatId;
    private Integer score;
    private String feedback;
    private String queryText;
    private QueryResult queryResult;
    private Map<String, String> difyParmes;
    private List<SemanticParseInfo> parseInfos;
    private List<SimilarQueryRecallResp> similarQueries;
    private ParseTimeCostResp parseTimeCost = new ParseTimeCostResp();
}
