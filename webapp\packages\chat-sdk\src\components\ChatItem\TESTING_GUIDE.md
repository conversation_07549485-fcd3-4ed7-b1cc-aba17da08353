# ChatItem 思维链功能测试指南

## 测试要点

### 1. Loading效果位置测试
**预期效果**：
- 意图分类进行中时，应该显示"意图分类中 ⚪⚪⚪"（Loading在后面）
- 而不是"⚪⚪⚪ 意图分类中"（Loading在前面）

**测试步骤**：
1. 发送新消息
2. 观察意图分类组件的Loading显示位置
3. 确认Loading三个点在"意图分类中"文字后面

### 2. 自动滚动测试
**预期效果**：
- 思维链组件逐步显示时，页面自动滚动到底部
- 用户无需手动滚动就能看到新显示的组件

**测试步骤**：
1. 确保聊天容器有足够高度，使得组件显示时会超出视窗
2. 发送新消息
3. 观察每个组件显示时是否自动滚动
4. 确认用户始终能看到最新显示的组件

### 3. 思维链完整流程测试
**预期效果**：
- 新消息：意图分类(1秒) → 维度指标(+1秒) → 生成SQL(+1秒) → 数据查询(+1秒)
- 每个步骤显示时自动滚动
- 只执行一次，不循环

**测试步骤**：
1. 发送新消息
2. 观察组件按时间间隔依次显示
3. 确认每个组件显示时页面自动滚动
4. 确认思维链只执行一次，不重复

### 4. 页面刷新测试
**预期效果**：
- 页面刷新后，所有组件立即显示
- 无思维链动画，无闪动

**测试步骤**：
1. 发送消息并等待完成
2. 刷新页面
3. 确认所有组件立即显示，无动画延迟

### 5. 切换智能体测试
**预期效果**：
- 切换智能体时，组件立即显示
- 无思维链动画

**测试步骤**：
1. 在有消息的情况下切换智能体
2. 确认组件立即显示，无动画延迟

## 常见问题排查

### 问题1：Loading位置不正确
**症状**：Loading显示在"意图分类中"前面
**排查**：检查IntentTip组件的title-bar结构是否正确修改

### 问题2：页面不自动滚动
**症状**：组件显示时页面不滚动，需要手动滚动
**排查**：
1. 检查onUpdateMessageScroll回调是否正确传入
2. 检查requestAnimationFrame是否正确调用
3. 检查父组件是否正确处理滚动逻辑

### 问题3：思维链重复执行
**症状**：组件显示完成后又重新开始显示
**排查**：
1. 检查thinkingChainStarted状态是否正确设置
2. 检查startThinkingChain函数的重复执行检查

### 问题4：组件闪动
**症状**：页面刷新时组件快速闪动
**排查**：
1. 检查enableThinkingChain状态在页面刷新时是否正确设置为false
2. 检查thinkingChainStep是否正确设置为3

## 测试环境要求

1. **容器高度**：确保聊天容器有足够高度，使得组件显示时会超出视窗
2. **滚动容器**：确保有正确的滚动容器和滚动逻辑
3. **回调函数**：确保onUpdateMessageScroll回调正确传入并实现

## 性能注意事项

1. **requestAnimationFrame**：使用requestAnimationFrame确保滚动在DOM更新后执行
2. **防抖处理**：避免频繁的滚动调用
3. **内存清理**：确保setTimeout在组件卸载时正确清理

## 兼容性测试

1. **不同浏览器**：测试Chrome、Firefox、Safari等主流浏览器
2. **移动端**：测试移动端的滚动行为
3. **不同屏幕尺寸**：测试不同屏幕尺寸下的显示效果
