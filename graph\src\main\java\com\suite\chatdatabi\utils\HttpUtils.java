//package com.suite.chatdatabi.utils;
//
////import cn.hutool.core.map.MapUtil;
////import cn.hutool.http.*;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.HashMap;
//import java.util.Map;
//
//
//@Slf4j
//public class HttpUtils {
//
//    private static final Map<String,String> initHeaderMap= new HashMap<>();
//    static {
//        initHeaderMap.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
//    }
//
//    /**
//     * get请求
//     * @param url 路径
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T> JsonResult<T> httpGet(String url, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.get(url)
//                    .headerMap(initHeaderMap,false)
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("GET请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("GET请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * get请求
//     * @param url 路径
//     * @param param 请求参数
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T> JsonResult<T> httpGet(String url, Map<String,Object> param,Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.get(url)
//                    .headerMap(initHeaderMap,false)
//                    .form(param)
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("GET请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("GET请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * get请求自定义header
//     * @param url 路径
//     * @param param 请求参数
//     * @param header 请求头
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T> JsonResult<T> httpGet(String url,Map<String,Object> param,Map<String,String> header, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.get(url)
//                    .headerMap(MapUtil.isEmpty(header)?initHeaderMap:header,false)
//                    .form(param)
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("GET请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("GET请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * post请求
//     * @param url 路径
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T> JsonResult<T> httpPost(String url,Map<String,Object> param, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.post(url)
//                    .headerMap(initHeaderMap,false)
//                    .form(param)
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("POST请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("POST请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * post请求
//     * @param url 路径
//     * @param header 请求头
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T> JsonResult<T> httpPost(String url,Map<String,Object> param,Map<String,String> header, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.post(url)
//                    .headerMap(MapUtil.isEmpty(header)?initHeaderMap:header,false)
//                    .form(param)
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("POST请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("POST请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * post请求
//     * @param url 路径
//     * @param param 请求传参
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T,E> JsonResult<T> httpPost(String url,E param, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.post(url)
//                    .headerMap(initHeaderMap,false)
//                    .body(JSON.toJSONString(param))
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("POST请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("POST请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//
//    /**
//     * post请求
//     * @param url 路径
//     * @param param 请求传参
//     * @param header 请求头
//     * @param clazz 返回实体
//     * @param <T> 自定义实体
//     * @return T
//     */
//    public static <T,E> JsonResult<T> httpPost(String url,E param,Map<String,String> header, Class<T> clazz){
//        try{
//            HttpResponse getResponse=HttpRequest.post(url)
//                    .headerMap(MapUtil.isEmpty(header)?initHeaderMap:header,false)
//                    .body(JSON.toJSONString(param))
//                    .execute();
//            String body=getResponse.body();
//            if(getResponse.isOk()){
//                return ResultTool.success(JSONObject.parseObject(body, clazz));
//            }
//            log.info("POST请求错误：返回状态码：{}，返回信息：{}",getResponse.getStatus(),body);
//            return ResultTool.fail(false,JSONObject.parseObject(body, clazz));
//        }catch (HttpException e){
//            log.error("POST请求失败：{}",e.getMessage());
//            return ResultTool.fail();
//        }
//    }
//}