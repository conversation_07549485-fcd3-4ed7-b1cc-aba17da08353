import type { FC } from 'react';
import { Avatar } from 'antd';
import type { AvatarProps } from 'antd';
import userAvatarIcon from '../../assets/icon/user-avatar.svg';

interface Props extends AvatarProps {
  staffName?: string;
  avatarImg?: string;
}
const { tmeAvatarUrl } = process.env;
const TMEAvatar: FC<Props> = ({ staffName, avatarImg, ...restProps }) => {
  const avatarSrc = tmeAvatarUrl ? `${tmeAvatarUrl}${staffName}.png` : avatarImg;
  return (
    <Avatar
      src={`${avatarSrc}`}
      alt="avatar"
      icon={<img src={userAvatarIcon} alt="user avatar" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />}
      shape="circle"
      {...restProps}
    />
  );
};
export default TMEAvatar;
