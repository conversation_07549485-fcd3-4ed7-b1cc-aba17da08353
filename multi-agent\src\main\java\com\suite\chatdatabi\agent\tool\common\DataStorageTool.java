package com.suite.chatdatabi.agent.tool.common;


import com.suite.chatdatabi.agent.agent.AgentContext;
import com.suite.chatdatabi.agent.tool.BaseTool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Slf4j
public class DataStorageTool implements BaseTool {

    private AgentContext agentContext;
    // 使用静态Map存储查询结果，实际应用中可以使用Redis或数据库
    private static final Map<String, Map<String, Object>> DATA_STORAGE = new ConcurrentHashMap<>();

    @Override
    public String getName() {
        return "data_storage_tool";
    }

    @Override
    public String getDescription() {
        return "数据存储工具，用于存储SQL查询结果供后续报告生成使用";
    }

    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("type", "object");
        Map<String, Object> properties = new HashMap<>();

        Map<String, Object> actionParam = new HashMap<>();
        actionParam.put("type", "string");
        actionParam.put("enum", new String[]{"store", "retrieve", "list"});
        actionParam.put("description", "操作类型：store存储数据，retrieve获取数据，list列出所有数据键");
        properties.put("action", actionParam);

        Map<String, Object> keyParam = new HashMap<>();
        keyParam.put("type", "string");
        keyParam.put("description", "数据存储键名");
        properties.put("data_key", keyParam);

        Map<String, Object> dataParam = new HashMap<>();
        dataParam.put("type", "object");
        dataParam.put("description", "要存储的数据");
        properties.put("data", dataParam);

        params.put("properties", properties);
        params.put("required", new String[]{"action"});
        return params;
    }

    @Override
    public Object execute(Object input) {
        Map<String, Object> inputMap = (Map<String, Object>) input;
        String action = (String) inputMap.get("action");
        String dataKey = (String) inputMap.get("data_key");
        Object data = inputMap.get("data");

        String sessionId = agentContext.getSessionId();
        String storageKey = sessionId + "_" + dataKey;

        switch (action) {
            case "store":
                if (dataKey != null && data != null) {
                    Map<String, Object> storageData = new HashMap<>();
                    storageData.put("data", data);
                    storageData.put("timestamp", System.currentTimeMillis());
                    storageData.put("session_id", sessionId);
                    DATA_STORAGE.put(storageKey, storageData);
                    log.info("存储数据成功，键: {}", storageKey);
                    return "数据存储成功，键: " + dataKey;
                }
                return "存储失败：缺少数据键或数据";

            case "retrieve":
                if (dataKey != null) {
                    Map<String, Object> storedData = DATA_STORAGE.get(storageKey);
                    if (storedData != null) {
                        return storedData.get("data");
                    }
                }
                return "未找到指定键的数据";

            case "list":
                List<String> keys = new ArrayList<>();
                for (String key : DATA_STORAGE.keySet()) {
                    if (key.startsWith(sessionId + "_")) {
                        keys.add(key.substring((sessionId + "_").length()));
                    }
                }
                return "当前会话存储的数据键: " + String.join(", ", keys);

            default:
                return "不支持的操作类型";
        }
    }

    // 静态方法供其他工具调用
    public static void storeData(String sessionId, String key, Object data) {
        String storageKey = sessionId + "_" + key;
        Map<String, Object> storageData = new HashMap<>();
        storageData.put("data", data);
        storageData.put("timestamp", System.currentTimeMillis());
        storageData.put("session_id", sessionId);
        DATA_STORAGE.put(storageKey, storageData);
    }

    public static Object retrieveData(String sessionId, String key) {
        String storageKey = sessionId + "_" + key;
        Map<String, Object> storedData = DATA_STORAGE.get(storageKey);
        return storedData != null ? storedData.get("data") : null;
    }
}